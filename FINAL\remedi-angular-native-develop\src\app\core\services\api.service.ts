// Updated ApiService for Angular 20 with best practices
import { Injectable } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpHeaders,
  HttpParams,
  HttpBackend
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class ApiService {
  private readonly jsonHeaders = new HttpHeaders({ 'Content-Type': 'application/json' });
  private readonly formHeaders = new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' });
  private readonly textHeaders = new HttpHeaders({ 'Content-Type': 'text/plain' });

  constructor(
    private _http: HttpClient,
    private handler: HttpBackend
  ) { }

  post<T>(url: string, data: any, useBasic = false, useForm = false): Observable<T> {
    const endpoint = (useBasic ? environment.APIBaseURLBasic : environment.APIBaseURL) + url;

    // Conditionally set body and headers
    const body = data == null ? null : (useForm ? this.toFormUrlEncoded(data) : data);
    const headers = data == null ? undefined : (useForm ? this.formHeaders : this.jsonHeaders);

    return this._http.post<T>(endpoint, body, { headers }).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

   postWithTextResponse(url: string, data: any, useBasic = false, useForm = false): Observable<string> {
 // const endpoint = (useBasic ? environment.APIBaseURLBasic : environment.APIBaseURL) + url;
  
  // Conditionally set body and headers (consistent with main post method)
  const body = data == null ? null : (useForm ? this.toFormUrlEncoded(data) : data);
  const headers = data == null ? undefined : (useForm ? this.formHeaders : this.jsonHeaders);
  return this._http.post(url, body, {
    headers,
    responseType: 'text'
  }).pipe(
    catchError(this.handleError)
  );
}

  get<T>(url: string, params: string = '', useBasic = false): Observable<T> {
    const endpoint = (useBasic ? environment.APIBaseURLBasic : environment.APIBaseURL) + url + params;
    return this._http.get<T>(endpoint).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

  postText<T>(url: string, data: any): Observable<T> {
    return this._http.post<T>(environment.APIBaseURLBasic + url, data, {
      headers: this.textHeaders
    }).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

  put<T>(url: string, data: any): Observable<T> {
    return this._http.put<T>(environment.APIBaseURL + url, data, {
      headers: this.jsonHeaders
    }).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

  postServiceByQueryBasic<T>(url: string, queryParams: string): Observable<T> {
    const fullUrl = environment.APIBaseURLBasic + url + queryParams;
    return this._http.post<T>(fullUrl, '').pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }


  delete<T>(url: string, params: string = ''): Observable<T> {
    return this._http.delete<T>(environment.APIBaseURL + url + params).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

  postWithoutInterceptor<T>(url: string, params: string = ''): Observable<T> {
    const httpClient = new HttpClient(this.handler);
    return httpClient.post<T>(environment.APIBaseURL + url + params, '', {
      responseType: 'text' as 'json'
    }).pipe(
      map(this.handleSuccess),
      catchError(this.handleError)
    );
  }

  private toFormUrlEncoded(data: any): string {
    const params = new URLSearchParams();
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key) && data[key] !== undefined && data[key] !== null) {
        params.append(key, data[key]);
      }
    }
    return params.toString();
  }

  private handleSuccess<T>(res: T): T {
    return res;
  }

  private handleError(error: HttpErrorResponse) {
    // Improved error logging to prevent [object Object] in console
    const errorMessage = this.stringifyError(error);
    console.error('API Error:', errorMessage);

    // Log additional details for debugging
    if (error.error) {
      console.error('API Error Details:', this.stringifyError(error.error));
    }

    if (error.url) {
      console.error('API Error URL:', error.url);
    }

    if (error.status) {
      console.error('API Error Status:', error.status, error.statusText);
    }

    return throwError(() => error);
  }

  /**
   * Safely stringify errors for better logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return `${error.name}: ${error.message}`;
    }

    if (typeof error === 'object' && error !== null) {
      try {
        // Handle HttpErrorResponse specifically
        if (error.error && error.status && error.url) {
          return `HTTP ${error.status} ${error.statusText || ''} at ${error.url}: ${this.stringifyError(error.error)}`;
        }
        return JSON.stringify(error, null, 2);
      } catch {
        return String(error);
      }
    }

    return String(error);
  }

  
  postServiceJson<T>(url: string, data: any): Observable<T> {
  return this._http.post(url, data, { responseType: 'text' as 'json' }).pipe(
    map((res) => res as T), 
    catchError((err) => throwError(() => err))
  );
}

}
