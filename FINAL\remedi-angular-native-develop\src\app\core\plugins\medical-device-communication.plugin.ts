import { registerPlugin, PluginListenerHandle } from '@capacitor/core';
import { DeviceType } from '../interfaces/medical-device.interface';

/**
 * Medical Device Communication Plugin Interface
 * Enterprise-level Capacitor plugin for cross-platform medical device communication
 */

export interface MedicalDeviceLaunchOptions {
    deviceType: DeviceType;
    packageName: string;
    className: string;
    patientId: string;
    language?: string;
    realId?: string;
    sessionId?: string;
    extras?: { [key: string]: any };
}

export interface MedicalDeviceResult {
    success: boolean;
    deviceType: DeviceType;
    data?: any;
    error?: string;
    timestamp: number;
    platform?: string;
    communicationMethod?: string;
    sessionId?: string;
    cancelled?: boolean;
    resultCode?: number;
}

export interface PulseOximeterResult {
    spo2: string;
    pulse_rate: string;
    battery_level?: string;
    signal_quality?: string;
}

export interface StethoscopeResult {
    audio_data: ArrayBuffer;
    duration: number;
    sample_rate: number;
    heart_rate?: string;
}

// export interface ThermometerResult {
//     temperature: string;
//     unit: string;
//     battery_level?: string;
// }


export interface ThermometerResult {
  temperature?: number; // ✅ Add this to hold the final parsed value
  unit?: 'celsius' | 'fahrenheit';
  fahrenheit?: number;
  celcius?: number;
  timestamp: number;
  batteryLevel?: number;
  sensorType?: string;
  error?: string;
}


export interface BloodPressureResult {
    systolic: string;
    diastolic: string;
    pulse: string;
    battery_level?: string;
}

export interface ECGResult {
    waveform_data: number[];
    heart_rate: string;
    duration: number;
    leads?: string[];
}

export interface OpticalReaderResult {
    success: boolean;
    deviceType: string;
    sensor_type?: string;
    test_name: string;
    test_result: string;
    optical_image_path?: string;
    image_name?: string;
    image_file_uri?: string;
    image_uri_parsed?: boolean;
    session_id?: string;
    timestamp?: number;
    source?: string;
    data?: {
        test_name: string;
        test_result: string;
        optical_image_path?: string;
        image_name?: string;
        image_file_uri?: string;
        sensor_type?: string;
        [key: string]: any;
    };
}
export interface HemoResult {
    hemoglobin_value: string;
}

export interface MedicalDeviceCommunicationPlugin {
    /**
     * Launch a medical device application
     */
    launchDevice(options: MedicalDeviceLaunchOptions): Promise<MedicalDeviceResult>;

    /**
     * Get supported devices
     */
    getSupportedDevices(): Promise<{ devices: string[]; platform: string; novaICareInstalled: boolean; novaICareVersion: string }>;

    /**
     * Check if a device is available
     */
    isDeviceAvailable(options: { deviceType: DeviceType }): Promise<{ 
        available: boolean;
        novaICareInstalled: boolean;
        deviceSupported: boolean;
        novaICareVersion: string;
    }>;

    /**
     * Get device status
     */
    getDeviceStatus(options: { deviceType: DeviceType }): Promise<{
        connected: boolean;
        batteryLevel?: number;
        signalStrength?: number;
    }>;

    /**
     * Disconnect from device
     */
    disconnectDevice(options: { deviceType: DeviceType }): Promise<{ success: boolean }>;

    /**
     * Add listener for pulse oximeter results
     */
    addListener(
        eventName: 'PulseOximeterResult',
        listenerFunc: (result: PulseOximeterResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for stethoscope results
     */
    addListener(
        eventName: 'StethoscopeResult',
        listenerFunc: (result: StethoscopeResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for thermometer results
     */
    addListener(
        eventName: 'ThermometerResult',
        listenerFunc: (result: ThermometerResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for blood pressure results
     */
    addListener(
        eventName: 'BloodPressureResult',
        listenerFunc: (result: BloodPressureResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for ECG results
     */
    addListener(
        eventName: 'ECGResult',
        listenerFunc: (result: ECGResult) => void
    ): Promise<PluginListenerHandle>;

    addListener(
        eventName: 'HemoResult',
        listenerFunc: (result: HemoResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for general device results
     */
    addListener(
        eventName: 'DeviceResult',
        listenerFunc: (result: MedicalDeviceResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Add listener for device errors
     */
    addListener(
        eventName: 'DeviceError',
        listenerFunc: (error: { deviceType: DeviceType; message: string; code?: string }) => void
    ): Promise<PluginListenerHandle>;

    addListener(
         eventName: 'opticalreaderlis',
         listenerFunc: (result: OpticalReaderResult) => void
    ): Promise<PluginListenerHandle>;

    /**
     * Remove all listeners
     */
    removeAllListeners(): Promise<void>;
}

/**
 * Register the plugin
 */
export const MedicalDeviceCommunicationPlugin = registerPlugin<MedicalDeviceCommunicationPlugin>(
    'MedicalDeviceCommunicationPlugin'
);
