:host {
    display: block;
    // padding: 10px;
    background-color: #f7f7f7;
}

.spo2-battery {
    display: flex;
    align-items: center;
}

.batteryContainer {
    width: 40px;
    height: 12px;
    border: 1px solid #000;
    background-color: #e0e0e0;
    position: relative;
}

.batteryOuter {
    width: 100%;
    height: 100%;
    background: lightgreen;
    overflow: hidden;
}

#batteryLevel {
    height: 100%;
    background-color: green;
    transition: width 0.3s ease;
}

.batteryBump {
    width: 4px;
    height: 6px;
    background: #000;
    position: absolute;
    right: -5px;
    top: 3px;
}

.spo2-title {
    flex-grow: 1;
    text-align: center;
    font-weight: bold;
    font-size: 20px;
    margin-left: -40px; // to offset battery width
}

.spo2-close-btn {
    border: none;
    background: transparent;
    color: white;
    font-size: 20px;
    cursor: pointer;
}


