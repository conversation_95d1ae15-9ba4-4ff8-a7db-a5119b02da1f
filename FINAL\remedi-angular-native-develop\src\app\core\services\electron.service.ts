import { Injectable } from '@angular/core';

declare global {
    interface Window {
        electronAPI?: {
            isElectron: boolean;
            platform: string;
            version: string;
        };
    }
}

@Injectable({
    providedIn: 'root'
})
export class ElectronService {

    constructor() { }

    /**
     * Check if the app is running in Electron
     */
    isElectron(): boolean {
        return !!(window && window.electronAPI && window.electronAPI.isElectron);
    }

    /**
     * Get the platform when running in Electron
     */
    getPlatform(): string {
        return this.isElectron() ? window.electronAPI!.platform : 'web';
    }

    /**
     * Get Electron version
     */
    getElectronVersion(): string {
        return this.isElectron() ? window.electronAPI!.version : '';
    }

    /**
     * Get appropriate base URL for API calls
     */
    getLocationOrigin(): string {
        if (this.isElectron()) {
            // In Electron, we need to use the actual domain instead of file:// protocol
            return 'https://s3test2.remedi.co.in';
        }
        return window.location.origin;
    }

    /**
     * Get appropriate hostname for API calls
     */
    getLocationHostname(): string {
        if (this.isElectron()) {
            // In Electron, return the actual API hostname
            return 's3test2.remedi.co.in';
        }
        return window.location.hostname;
    }

    /**
     * Get appropriate pathname
     */
    getLocationPathname(): string {
        if (this.isElectron()) {
            // In Electron, use a default path
            return '/remediprmsang';
        }
        return window.location.pathname;
    }

    /**
     * Check if we're in development mode
     */
    isDevelopment(): boolean {
        if (this.isElectron()) {
            // In Electron, check if dev tools are available or use a different method
            return !!(window as any).webContents?.isDevToolsOpened?.() ||
                location.protocol === 'file:';
        }
        return location.hostname === 's3test2.remedi.co.in' || location.hostname === '127.0.0.1';
    }
}