.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
}

.login-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 30px 40px;
  width: 320px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.qr-code {
  width: 100px;
  margin: 0 auto 20px auto;
  display: block;
}

.input-group {
  position: relative;
  margin-bottom: 20px;
}

.input-group input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.input-group .icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #888;
}

.user-icon::before {
  content: '\f007';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.lock-icon::before {
  content: '\f023';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.actions .forgot {
  font-size: 13px;
  color: #4d8cff;
  cursor: pointer;
  text-decoration: none;
}

.login-btn {
  width: 100%;
  padding: 10px;
  background-color: #4d8cff;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  font-size: 15px;
  cursor: pointer;
}

.login-btn:hover {
  background-color: #4079e3;
}

.privacy {
  display: block;
  margin-top: 10px;
  font-size: 12px;
  color: #555;
  text-decoration: underline;
  cursor: pointer;
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

.login-error {
  color: red;
  margin-top: 10px;
  text-align: center;
}
