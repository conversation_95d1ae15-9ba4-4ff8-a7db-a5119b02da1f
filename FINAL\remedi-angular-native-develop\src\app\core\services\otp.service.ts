import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ConstantService } from './constant.service';
import { Observable } from 'rxjs';

export interface OtpResponse {
  request_id?: string;
  type?: 'success' | 'failed';
  msg?: string;
  message?: string;
  status?: string;
}

@Injectable({
  providedIn: 'root',
})
export class OtpService {
  domainName = environment.apiDomain;

  constructor(
    private http: HttpClient,
    private constantSvc: ConstantService
  ) {}

// sendOtp(username: string): Observable<OtpResponse> {
//   const query = `?username=${username}&domain=${this.domainName}&action=sendotpbyusername`;
//   return this.http.post<OtpResponse>(
//     this.constantSvc.APIConfig.GETCOMMONSERVICES + query,
//     null
//   );
// }


sendOtp(username: string): Observable<OtpResponse> {
  const query = `?username=${username}&domain=${this.domainName}&action=sendotpbyusername`;
  const fullUrl = this.constantSvc.APIConfig.API_BASE + this.constantSvc.APIConfig.GETCOMMONSERVICES + query;
  return this.http.post<OtpResponse>(fullUrl, null);
}



  verifyOtp(username: string, otp: string): Observable<OtpResponse> {
    const query = `?username=${username}&domain=${this.domainName}&action=verifyotpbyusername&otp=${otp}&token=null`;
    return this.http.post<OtpResponse>(
      this.constantSvc.APIConfig.GETCOMMONSERVICES + query,
      null
    );
  }
}
