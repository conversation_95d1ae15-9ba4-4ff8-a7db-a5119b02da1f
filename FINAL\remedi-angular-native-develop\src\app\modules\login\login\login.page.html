<div class="login-container">
    <div class="login-box">
        <form [formGroup]="loginForm" (ngSubmit)="login()">
            <div class="input-group">
                <i class="icon user-icon"></i>
                <input type="text" formControlName="username" placeholder="Username" required />
                <div class="error" *ngIf="submitted && loginForm.controls['username'].errors?.['required']">
                    Username is required
                </div>
            </div>

            <div class="input-group">
                <i class="icon lock-icon"></i>
                <input type="password" formControlName="password" placeholder="Password" required />
                <div class="error" *ngIf="submitted && loginForm.controls['password'].errors?.['required']">
                    Password is required
                </div>
            </div>

            <div class="actions">
                <a class="forgot" (click)="goToForgot()">Forgot Password?</a>
            </div>

            <button id="login-btn" type="submit" class="login-btn">Login</button>
            <a class="privacy">Privacy Policy</a>

            <div class="error" *ngIf="showLoginError">{{ showLoginMsg }}</div>
        </form>
    </div>
</div>

<!-- iCare Manual Entry -->
