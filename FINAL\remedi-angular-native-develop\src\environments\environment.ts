// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.
// ✅ Utility function to detect if running inside Android WebView
// ✅ 1. Detect if running inside an Android WebView
// ✅ 1. Utility: Detect if running on Android via Capacitor
function isAndroidWebView(): boolean {
  return typeof window !== 'undefined' &&
    !!(window as any).Capacitor &&
    (window as any).Capacitor.getPlatform?.() === 'android';
}
const url = 's3test2.remedi.co.in';  // ✅ Use only staging server

// ✅ 2. Choose correct IP or domain based on platform
// const url = isAndroidWebView() ? '*************' : 's3test2.remedi.co.in';  // Replace with your PC's IP

// ✅ 3. Helpers
function getLocationOrigin(): string {
  if ((window as any).electronAPI?.isElectron) {
    return 'https://' + url;
  }
  return typeof window !== 'undefined' ? window.location.origin : 'https://' + url;
}

function getLocationHostname(): string {
  if ((window as any).electronAPI?.isElectron) {
    return url;
  }
  return typeof window !== 'undefined' ? window.location.hostname : url;
}

function getLocationPathname(): string {
  if ((window as any).electronAPI?.isElectron) {
    return '/remediprmsang';
  }
  return typeof window !== 'undefined' ? window.location.pathname : '/remediprmsang';
}

// ✅ 4. Full environment config
export const environment = {
  production: false,

  locationURL: getLocationOrigin(),
  APILoginBaseURL: 'https://' + url + '/RemediPRMSTest/',
  APIBaseURL: 'https://' + url + '/RemediPRMS/',
  APIBaseURLBasic: 'https://' + url + '/RemediPRMS/',
  APIBaseURLBasicPass: 'https://' + url + '/RemediPRMS',
  apiDomain: url.split('.')[0],
  pathName: getLocationPathname(),
  soundURL: getLocationOrigin(),

  getTimeOffSet() {
    return sessionStorage.getItem('offSetTimeZone');
  }
};

// ✅ Optional: Debug helper
console.log('🌐 environment.APIBaseURL =', environment.APIBaseURL);


/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.