import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { IonicModule } from '@ionic/angular';
import { Spo2DevicePage } from '../../prms/ble-device/spo2-device/spo2-device.page';
import { WebsocketsService } from 'src/app/core/services/websocket.service';
import { Subscription } from 'rxjs';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { Spo2ManualEntryPage } from '../ble-device/spo2-manual-entry/spo2-manual-entry.page';
import { ThermometerPage } from '../ble-device/thermometer/thermometer.page';
 import { EcgJettyDevicePage } from '../ble-device/ecg-jetty-device/ecg-jetty-device.page';
import { Router, RouterModule } from '@angular/router';
import { ApiService } from 'src/app/core/services/api.service';
import { ConstantService } from 'src/app/core/services/constant.service';
import { IcareSpo2Page } from '../ble-device/icare-spo2/icare-spo2.page';
import { IcareThermometerPage } from '../ble-device/icare-thermometer/icare-thermometer.page';
import { RapidTestPage } from '../../prms/usb-device/rapid-test/rapid-test.page';
import { IcareRapidTestPage } from '../../prms/usb-device/icare-rapid-test/icare-rapid-test.page';
import { HemoglobinPage } from '../usb-device/hemoglobin/hemoglobin.page';
import { EcgIcareDevicePage } from '../ble-device/ecg-icare-device/ecg-icare-device.page';
import { StethoscopeDevicePage } from '../ble-device/stethoscope-device/stethoscope-device.page';






@Component({
  selector: 'app-parameter',
  templateUrl: './parameter.page.html',
  styleUrls: ['./parameter.page.scss'],
  standalone: true,
  imports: [IonicModule, FormsModule, MatDialogModule, RouterModule, CommonModule],

})
export class ParameterPage implements OnInit {
  valSubscription!: Subscription;
  isRapidTestKitReady = false;
  private rapidTestDialogOpen = false;
  jettyMessage: string | null = null;
  loginTime: any;
  username: any;
  currentUser: any;
  patientData: any;
  subscription5!: Subscription;




  constructor(
    private WebsocketsService: WebsocketsService,
    private dialog: MatDialog,
    private router: Router,
    private apiSvc: ApiService,
    private constantSvc: ConstantService,



  ) { }
  // ngOnInit() {
  //   const currentUser = JSON.parse(sessionStorage.getItem("USER") || '{}');
  //   const patientData = JSON.parse(sessionStorage.getItem("patientDetails") || '{}');

  //   this.WebsocketsService.connectBleSocket(); // 🔁 Connect BLE socket
  //   this.WebsocketsService.connectParamSocket(); // 🔁 Connect Parameter socket

  //   // Safely assign user data
  //   this.username = currentUser?.profiledetail?.userName || '';
  //   this.loginTime = currentUser?.commondetail?.logintime || '';

  //   setTimeout(() => {
  //     this.WebsocketsService.sendParamMessage("connectKitFirstTime");
  //     this.WebsocketsService.sendBleMessage("BleAppletInit");

  //     this.WebsocketsService.bleValueObs$.subscribe((data) => {
  //       console.log("BLE Message:", data);

  //       if (data === "msgFromBLEApplet~false~Dongle not connected.") {
  //         console.warn("Dongle is not connected.");
  //         // TODO: show alert, update UI, etc.
  //       } else if (data === "msgFromBLEApplet~true~Dongle connected successfully.~1") {
  //         console.log("Dongle connected successfully.");
  //         // TODO: set UI status to connected, etc.
  //       } else if (data === "error") {
  //         console.error("BLE connection error.");
  //         // TODO: handle error properly
  //       }
  //     });
  //   }, 1000);
  // }

  ngOnInit() {
    const currentUser = JSON.parse(sessionStorage.getItem("USER") || '{}');
    const patientData = JSON.parse(sessionStorage.getItem("patientDetails") || '{}');

    this.currentUser = currentUser;
    this.patientData = patientData;

    this.username = currentUser?.profiledetail?.userName || '';
    this.loginTime = currentUser?.logintime || ''; // ✅ Fix is here

    this.WebsocketsService.connectBleSocket();
    this.WebsocketsService.connectParamSocket();

    setTimeout(() => {
      //this.WebsocketsService.sendParamMessage("connectKitFirstTime");
      this.WebsocketsService.sendBleMessage("BleAppletInit");

      this.WebsocketsService.bleValueObs$.subscribe((data) => {
        console.log("BLE Message:", data);

        if (data === "msgFromBLEApplet~false~Dongle not connected.") {
          console.warn("Dongle is not connected.");
        } else if (data === "msgFromBLEApplet~true~Dongle connected successfully.~1") {
          console.log("Dongle connected successfully.");
        } else if (data === "error") {
          console.error("BLE connection error.");
        }
      });
      this.WebsocketsService.sendParamMessage("connectKitFirstTime");
      this.WebsocketsService.paramValueObs$.subscribe((data) => {
        console.log("param data", data);

        if (data == null || data == "null") {
          console.log("usb  Test not available - Please start the SDK");
          this.isRapidTestKitReady = false;
        }
        else if (data === "kitStatusCheck~1001") {
          console.log("usb Test ready!");
          this.isRapidTestKitReady = true;
        }
        else if (data == "error") {
          console.log("usb Test has error!");
          this.isRapidTestKitReady = false;
        }
      });

    }, 1000);
  }




  handleJettyClick(deviceName: string, methodName: string | null) {
    this.jettyMessage = `Please Switch On ${deviceName} Device`;

    if (methodName && typeof (this as any)[methodName] === 'function') {
      (this as any)[methodName]();
    }

    setTimeout(() => {
      this.jettyMessage = null;
    }, 3000);
  }

  async openSpo2ComponentDevice() {
    console.log("Image clicked!");
    this.WebsocketsService.sendBleMessage("startScanFromHtml~50");

    try {
      this.valSubscription = this.WebsocketsService.bleValueObs$.subscribe(async (data) => {
        if (data == "errorLableUpdate~start scan status : Success!") {
          // handle
        } else if (data == "errorLableUpdate~SPO2Sensor connected successfully.") {
          // handle
        } else if (data == "SensorConnected~SPO2Sensor~50") {
          const dialogRef = this.dialog.open(Spo2DevicePage, {
            panelClass: 'spo2-dialog-panel',
            width: '740px',
            height: '400px',
            disableClose: true, // optional: disables clicking outside
            data: {
              api: 'withoutApiCalling'
            }
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result != undefined) {
              // handle result
              this.WebsocketsService.sendBleMessage("ble_close");

            }
          });
        }
      });
    } catch (error) {
      console.error("Error in openSpo2ComponentDevice:", error);
    }
  }

  async openIcareSpo2Page() {
    const dialogRef = this.dialog.open(IcareSpo2Page, {
      width: "600px",
      height: "auto",
      disableClose: true,
      // panelClass: 'slot-dialog'
    });

    dialogRef.afterClosed().subscribe((result) => {

    });
  }


  async openIcareThermometerPage() {
    const dialogRef = this.dialog.open(IcareThermometerPage, {
      width: "600px",
      height: "auto",
      disableClose: true,
      // panelClass: 'slot-dialog'
    });

    dialogRef.afterClosed().subscribe((result) => {

    });
  }


  async openThermometerPage() {
    console.log("Image clicked!");
    this.WebsocketsService.sendBleMessage("startScanFromHtml~40");

    try {
      this.valSubscription = this.WebsocketsService.bleValueObs$.subscribe(async (data) => {
        if (data == "errorLableUpdate~start scan status : Success!") {
          // handle
        } else if (data == "errorLableUpdate~THERMOSensor connected successfully.") {
          // handle
        } else if (data == "SensorConnected~THERMOSensor~40") {
          const dialogRef = this.dialog.open(ThermometerPage, {
            panelClass: 'spo2-dialog-panel',
            width: '750px',
            height: '500px',
            disableClose: true, // optional: disables clicking outside
            data: {
              api: 'withoutApiCalling'
            }
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result != undefined) {
              // handle result
              this.WebsocketsService.sendBleMessage("ble_close");

            }
          });
        }
      });
    } catch (error) {
      console.error("Error in openSpo2ComponentDevice:", error);
    }
  }

 async openEcgComponentDevice() {
   try {
     if (this.valSubscription) {
       this.valSubscription.unsubscribe();
     }

     this.valSubscription = this.WebsocketsService.bleValueObs$.subscribe((data) => {
       if (!data) return;

       const parts = data.split('~');
       const eventType = parts[0];
       const message = parts[1];

       if (eventType === 'errorLableUpdate' && message === 'ECGSensor connected successfully.') {
         console.log(' ECG Sensor Connected — opening ECG dialog');

         const dialogRef = this.dialog.open(EcgJettyDevicePage, {
           panelClass: 'ecg-dialog-panel',
           disableClose: true,
           data: {
             api: 'withoutApiCalling'
           }
         });

         dialogRef.afterClosed().subscribe((result) => {
           console.log(' ECG Dialog closed:', result);
           if (result === 'closedByUser') {
             // handle user-initiated close
           }
         });

         this.valSubscription.unsubscribe(); 
       }
     });

     this.WebsocketsService.sendBleMessage('startScanFromHtml~10');

   } catch (error) {
     console.error(' Error in openEcgComponentDevice:', error);
   }

 }

  openECGICarePage() {
    const dialogRef = this.dialog.open(EcgIcareDevicePage, {
      width: '600px',
      height: 'auto',
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      // Handle dialog result if needed
    });
  }


onLogout(): void {
  // Perform logout logic
  console.log('User logged out');
  // Example: Clear tokens or navigate to login
  this.router.navigate(['/login']);
}

// logout() {
//   const detail: any = JSON.parse(sessionStorage.getItem("USER"));
//   const patientData = JSON.parse(sessionStorage.getItem("patientDetails"));
//   let data = '';

//   if (sessionStorage.getItem("logoutConsultType") === '' && patientData?.consultationId) {
//     data = `?action=logoutservices&username=${this.username}&logintime=${this.loginTime}&consultationId=${patientData.consultationId}`;
//   } else {
//     data = `?action=logoutservices&username=${this.username}&logintime=${this.loginTime}`;
//   }

//   this.apiSvc
//     .postServiceByQueryBasic(this.constantSvc.APIConfig.GETCOMMONSERVICES, data)
//     .subscribe(
//       (res) => {
//         if (res?.status === 'success') {
//           console.log('Logout successful:', res);
//         } else {
//           console.log('Logout failed:', res);
//         }
//       },
//       (err) => {
//         console.error('Logout error:', err);
//       }
//     );
// }


  async openHemoglobinPage() {
    const dialogRef = this.dialog.open(HemoglobinPage, {
      width: "600px",
      height: "auto",
      disableClose: true,
      // panelClass: 'slot-dialog'
    });

    dialogRef.afterClosed().subscribe((result) => {

    });
  }


  openSpo2ManualEntryPage() {
    const dialogRef = this.dialog.open(Spo2ManualEntryPage, {
      width: '600px',
      height: 'auto',
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      // this.WebsocketsService.sendBleMessage("ble_close");
      this.WebsocketsService.sendParamMessage("ble_close");
      // Handle dialog result if needed
    });
  }

  async openRapidTestDevice() {
    console.log("Rapid test device clicked!");

    // Check if kit is ready
    if (!this.isRapidTestKitReady) {
      console.log("Rapid test kit not ready yet!");
      return;
    }

    // Check if dialog is already open
    if (this.rapidTestDialogOpen) {
      console.log("Rapid test dialog already open!");
      return;
    }

    try {
      console.log('✅ Kit ready - opening dialog...');

      // Set flag to prevent multiple dialogs
      this.rapidTestDialogOpen = true;

      // Open the dialog immediately - don't wait for WebSocket messages
      const dialogRef = this.dialog.open(RapidTestPage, {
        panelClass: 'rapid-test-dialog-panel',
        width: '840px',
        height: '360px',
        disableClose: true,
        data: {
          api: 'withoutApiCalling'
        }
      });

      dialogRef.afterClosed().subscribe((result) => {
        console.log('📱 Dialog closed');

        // Reset flag when dialog closes
        this.rapidTestDialogOpen = false;

        if (result != undefined) {
          // Handle result if needed
          console.log('Dialog result:', result);
        }
      });

    } catch (error) {
      console.error("Error in openRapidTestDevice:", error);
      // Reset flag on error
      this.rapidTestDialogOpen = false;
    }
  }

  openicarerapidtest() {
    const dialogRef = this.dialog.open(IcareRapidTestPage, {
      width: '600px',
      height: '700px',
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      // Handle dialog result if needed
    });
  }

  async openStethoscopeDevicePage() {
    this.WebsocketsService.sendBleMessage("startScanFromHtml~20");

    try {
      this.subscription5 = this.WebsocketsService.bleValueObs$.subscribe((data) => {
        console.log("Received BLE data:", data);
        if (data === "errorLableUpdate~start scan status : Success!") {
        } else if (data === "errorLableUpdate~StethoSensor connected successfully.") {
        } else if (data === "SensorConnected~StethoSensor~20") {

          const dialogRef = this.dialog.open(StethoscopeDevicePage, {
            width: "600px",
            height: "auto",
            disableClose: true,
            data: {
              api: "withoutApiCalling",
            },
          });

          dialogRef.afterClosed().subscribe((result) => {
            // this.WebsocketsService.sendBleMessage("disableNotificationFromHtml");

            if (result !== undefined) {
              this.WebsocketsService.sendBleMessage("ble_close");
            }
          });
        }
      });
    } catch (error) {
      console.error("Error in openStethoscopeDevicePage:", error);
    }
  }
logout() {
  const currentUser = JSON.parse(localStorage.getItem("USER") || '{}');
  const patientData = JSON.parse(localStorage.getItem("patientDetails") || '{}');
  const commonDetail = JSON.parse(localStorage.getItem("COMMON_DETAIL") || '{}');

  const doctorId = currentUser?.doctorId || '';
  const centerId = currentUser?.centerId || '';
  const userName = currentUser?.userName || '';
  const loginTime = commonDetail?.logintime || '';

  let queryParams = `?action=logoutservices&username=${userName}&doctorId=${doctorId}&centerId=${centerId}&logintime=${loginTime}`;

  if (
    localStorage.getItem("logoutConsultType") === '' &&
    patientData?.consultationId
  ) {
    queryParams += `&consultationId=${patientData.consultationId}`;
  }

  this.apiSvc
    .postServiceByQueryBasic(this.constantSvc.APIConfig.GETCOMMONSERVICES, queryParams)
    .subscribe(
      (res: any) => {
        if (res?.status === 'success') {
          console.log('✅ Logout successful:', res);
          this.router.navigate(['/login']);
        } else {
          console.log('❌ Logout failed:', res);
        }
      },
      (err) => {
        console.error('🔥 Logout error:', err);
      }
    );
}



}
