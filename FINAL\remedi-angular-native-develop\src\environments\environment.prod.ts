// Helper function to get location origin (Electron-compatible)
function getLocationOrigin(): string {
  if (typeof window !== 'undefined' && (window as any).electronAPI?.isElectron) {
    return 'https://s3test2.remedi.co.in';
  }
  return typeof window !== 'undefined' ? window.location.origin : 'https://s3test2.remedi.co.in';
}

// Helper function to get hostname (Electron-compatible)
function getLocationHostname(): string {
  if (typeof window !== 'undefined' && (window as any).electronAPI?.isElectron) {
    return 's3test2.remedi.co.in';
  }
  else {
    return 's3test2.remedi.co.in';

  }
  return typeof window !== 'undefined' ? window.location.hostname : 's3test2.remedi.co.in';
}

// Helper function to get pathname (Electron-compatible)
function getLocationPathname(): string {
  if (typeof window !== 'undefined' && (window as any).electronAPI?.isElectron) {
    return '/remediprmsang';
  }
  return typeof window !== 'undefined' ? window.location.pathname : '/remediprmsang';
}

// ✅ Use remote server for all platforms in development
const url = 's3test2.remedi.co.in';
export const environment = {
  production: true,
  locationURL: getLocationOrigin() + '/remediprmsang',
// getLocationHostname()
  APILoginBaseURL: 'https://' + url + '/RemediPRMSTest/',
  APIBaseURL: 'https://' + url + '/RemediPRMS/',
  APIBaseURLBasic: 'https://' + url + '/RemediPRMS/',
  APIBaseURLBasicPass: 'https://' + url + '/RemediPRMS',
  apiDomain: getLocationHostname().split('.')[0],
  pathName: getLocationPathname(),
  soundURL: getLocationOrigin() + '/remediprmsang',

  getTimeOffSet() {
    return sessionStorage.getItem("offSetTimeZone");
    // this.isDisabled = JSON.parse(sessionStorage.getItem("isDisabled"));

  }
};