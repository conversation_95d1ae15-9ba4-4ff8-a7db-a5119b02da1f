.spo2-dialog-panel .mat-dialog-container {
    padding: 0 !important;
    border-radius: 16px !important;
}

:host ::ng-deep .mat-mdc-dialog-surface {
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 0;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    position: relative;
    // overflow-y: hidden !important;
    outline: 0;
    transform: scale(0.8);
    transition: transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);
    max-height: inherit;
    min-height: inherit;
    min-width: inherit;
    max-width: inherit;
    box-shadow: var(--mat-dialog-container-elevation-shadow, none);
    border-radius: var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));
    background-color: var(--mat-dialog-container-color, var(--mat-sys-surface, white));
}

.pdp_titles {
    float: left;
    width: 100%;
    padding-bottom: 15px;
    position: relative;
    /* border-bottom: 1px solid #e5e5e5; */
    margin-bottom: 15px;
    margin-top: 11px;
    margin-left: 10px;
}