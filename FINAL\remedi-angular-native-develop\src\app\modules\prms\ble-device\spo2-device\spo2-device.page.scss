:host {
    display: block;
    padding: 0;
    margin: 0;
}

.spo2-dialog-wrapper {
    width: 100%;
    max-width: 740px;
    height: 450px;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    margin: 0 auto;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.25);
}

.spo2-header {
    background-color: #007bff;
    color: white;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    position: relative;
}

.spo2-battery {
    display: flex;
    align-items: center;
}

.batteryContainer {
    width: 40px;
    height: 12px;
    border: 1px solid #000;
    background-color: #e0e0e0;
    position: relative;
}

.batteryOuter {
    width: 100%;
    height: 100%;
    background: lightgreen;
    overflow: hidden;
}

#batteryLevel {
    height: 100%;
    background-color: green;
    transition: width 0.3s ease;
}

.batteryBump {
    width: 4px;
    height: 6px;
    background: #000;
    position: absolute;
    right: -5px;
    top: 3px;
}

.spo2-title {
    flex-grow: 1;
    text-align: center;
    font-weight: bold;
    font-size: 20px;
    margin-left: -40px; // to offset battery width
}

.spo2-close-btn {
    border: none;
    background: transparent;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.spo2-percentage {
    // text-align: center;
    font-size: 15px;
    margin: 15px 15px;
}

.spo2-chart-container {
    width: 100%;
    height: calc(100% - 100px);
}

:host ::ng-deep .highcharts-container {
    position: relative;
    overflow: hidden;
    width: 560px;
    height: 300px !important;
    text-align: left;
    line-height: normal;
    z-index: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    user-select: none;
    touch-action: manipulation;
    outline: none;
    padding: 0px;
}

 ::ng-deep .mdc-dialog--open .mat-mdc-dialog-surface,
.mdc-dialog--closing .mat-mdc-dialog-surface {
    transform: none;
    overflow-y: hidden !important
}