import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgZone } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { App } from '@capacitor/app';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { MedicalDeviceCommunicationService } from 'src/app/core/services/medical-device-communication.service';
import { DeviceDataManagerService } from 'src/app/core/services/device-data-manager.service';
import {
  DeviceType,
  ThermometerData,
} from 'src/app/core/interfaces/medical-device.interface';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-icare-thermometer',
  templateUrl: './icare-thermometer.page.html',
  styleUrls: ['./icare-thermometer.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, DatePipe],
})
export class IcareThermometerPage implements OnInit, OnDestroy {
  thermometerData: ThermometerData | null = null;
  resultdata: ThermometerData | null = null;
  showTemperatureResult: boolean = false;
  isReading: boolean = false;
  availableDevices: DeviceType[] = [];
  isLoadingDevices = true;

  private deviceDataSubscription?: Subscription;
  private appStateSubscription?: any;
  private centralizedSubscriptions: Subscription[] = [];

  constructor(
    private ngZone: NgZone,
    private medicalDeviceService: MedicalDeviceCommunicationService,
    private deviceDataManager: DeviceDataManagerService
  ) { }

  async ngOnInit() {
    console.log('IcareDevicePage initialized');

    // Load available devices first
    await this.loadAvailableDevices();

    // Register listeners for device results
    this.registerResultListener();
    this.registerMedicalDeviceListener();
    this.registerCentralizedDeviceListeners();

    // Register app state listener to re-register listeners when app becomes active
    this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. isActive:', isActive);
      if (isActive) {
        console.log('App is active again → re-registering listeners');
        this.registerResultListener();
        this.registerMedicalDeviceListener();
        this.registerCentralizedDeviceListeners();
      }
    });
  }

  /**
   * Load available devices from the device manager
   */
  async loadAvailableDevices() {
    try {
      console.log('Loading available devices...');
      this.isLoadingDevices = true;

      this.availableDevices = await this.deviceDataManager.getAvailableDevices();

      console.log('Available devices loaded:', this.availableDevices);
      this.isLoadingDevices = false;
    } catch (error) {
      console.error('Error loading available devices:', error);
      this.isLoadingDevices = false;

      // Fallback to showing all supported devices
      this.availableDevices = [
        DeviceType.PULSE_OXIMETER,
      ];
    }
  }

  ngOnDestroy() {
    console.log('IcareDevicePage destroyed - cleaning up listeners');

    // Clean up subscriptions
    if (this.deviceDataSubscription) {
      this.deviceDataSubscription.unsubscribe();
    }

    // Clean up centralized subscriptions
    this.centralizedSubscriptions.forEach(sub => sub.unsubscribe());
    this.centralizedSubscriptions = [];

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }

  /**
   * Register centralized device data listeners
   */
  registerCentralizedDeviceListeners() {
    try {
      console.log('Registering centralized device data listeners');

      const ThermometerSub = this.deviceDataManager.getDeviceData<ThermometerData>(DeviceType.THERMOMETER)
        .subscribe({
          next: (data) => {
            if (data) {
              this.ngZone.run(() => {
                this.resultdata = {
                  temperature: data.temperature || 0,
                  unit: data.unit || '°C',
                  timestamp: data.timestamp || Date.now(),
                  batteryLevel: data.batteryLevel,
                  signalQuality: data.signalQuality,
                  source: 'CentralizedManager'
                };
                console.log('ThermometerSub data from centralized manager:', this.resultdata);
                this.isReading = false;
              });
            }
          },
          error: (error) => console.error('Error in thermometer subscription:', error)
        });

      this.centralizedSubscriptions = [
        ThermometerSub,
      ];

      console.log('Centralized device listeners registered successfully');
    } catch (error) {
      console.error('Error registering centralized device listeners:', error);
    }
  }


  registerResultListener1() {
    try {
      NovaIcareLauncher.addListener('ThermometerResult', (data: any) => {
             console.log('📥 NovaIcareLauncher ThermometerResult event received:', JSON.stringify(data, null, 2));


        this.ngZone.run(() => {
          if (!data) {
            console.warn('Received null or undefined data from NovaIcareLauncher');
            this.resultdata = {
              temperature: 0,
              unit: 'celsius',
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'No data received'
            };
            return;
          }

          const hasCelcius = 'celcius' in data && data.celcius != null;
          const hasFahrenheit = 'fahrenheit' in data && data.fahrenheit != null;

          if (hasCelcius || hasFahrenheit) {
            const temperature = hasCelcius ? this.safeParseNumber(data.celcius, 0) : this.safeParseNumber(data.fahrenheit, 0);
            const unit: 'celsius' | 'fahrenheit' = hasCelcius ? 'celsius' : 'fahrenheit';

         this.resultdata = {
  temperature,
  unit,
  timestamp: Date.now(),
  source: 'NovaIcareLauncher',
  sensorType: data.sensor_type || 'Unknown' // <-- add this line
};


            console.log('✔️ Result data updated from NovaIcareLauncher:', this.resultdata);
          } else {
            this.resultdata = {
              temperature: 0,
              unit: 'celsius',
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'Temperature data missing'
            };
          }
        });
      });

      console.log('NovaIcareLauncher Thermometer listener registered');
    } catch (error) {
      console.error('Error registering NovaIcareLauncher listener:', this.stringifyError(error));
    }
  }
registerResultListener() {
  try {
    NovaIcareLauncher.addListener('ThermometerResult', (data: any) => {
      console.log('📥 NovaIcareLauncher ThermometerResult event received:', JSON.stringify(data, null, 2));

      this.ngZone.run(() => {
        if (!data) {
          console.warn('⚠️ Received null or undefined data from NovaIcareLauncher');
          this.resultdata = {
            temperature: 0,
            unit: 'celsius',
            timestamp: Date.now(),
            source: 'NovaIcareLauncher',
            error: 'No data received'
          };
          return;
        }

        const temperature = data.celcius ? parseFloat(data.celcius) :
                          data.fahrenheit ? Math.round(((parseFloat(data.fahrenheit) - 32) * 5 / 9) * 10) / 10 : 0;

        const thermometerData: ThermometerData = {
          temperature,
          unit: 'celsius',
          timestamp: Date.now(),
          batteryLevel: data.battery_level ? parseFloat(data.battery_level) : undefined,
          signalQuality: data.signal_quality ? parseFloat(data.signal_quality) : undefined,
          source: data.source || 'NovaIcareLauncher',
          error: data.error,
          sensorType: (data.sensor_type || '').toUpperCase() as DeviceType
        };

        this.resultdata = thermometerData;
        console.log("✅ Thermometer data processed:", thermometerData);

        // 👉 Add your condition here
        if (this.resultdata?.sensorType === DeviceType.THERMOMETER) {
          console.log("🎯 This is thermometer data. Proceeding with thermometer-specific logic.");
          // Add thermometer-specific handling here (e.g., save to API, update UI)
        }

      });
    });

    console.log('✅ NovaIcareLauncher Thermometer listener registered');
  } catch (error) {
    console.error('❌ Error registering NovaIcareLauncher listener:', this.stringifyError(error));
  }
}


  registerMedicalDeviceListener() {
    try {
      // Subscribe to medical device communication service
      this.deviceDataSubscription = this.medicalDeviceService.getDeviceDataObservable()
        .subscribe({
          next: (result) => {
            console.log('MedicalDeviceService result received:', result);

            if (result.success && result.deviceType === DeviceType.THERMOMETER && result.data) {
              this.ngZone.run(() => {
                const ThermometerData = result.data as ThermometerData;

                this.resultdata = {
                  temperature: ThermometerData.temperature,
                  unit: ThermometerData.unit, // required field
                  timestamp: ThermometerData.timestamp || Date.now(),
                  batteryLevel: ThermometerData.batteryLevel,
                  signalQuality: ThermometerData.signalQuality,
                  source: 'MedicalDeviceService'
                };

                console.log(' Result data updated from MedicalDeviceService:', this.resultdata);
              });
            } else if (!result.success) {
              console.warn('Device result failed:', result.error);

              // Show user-friendly error message
              this.ngZone.run(() => {
                this.resultdata = {
                  temperature: 0, // Required field with a fallback value
                  unit: 'celsius', // Must match the allowed string literal types
                  timestamp: Date.now(),
                  source: 'MedicalDeviceService',
                  error: result.error || 'Failed to get device result'
                };
              });
            }
          },
          error: (error) => {
            console.error('Error in medical device subscription:', error);

            this.ngZone.run(() => {
              this.resultdata = {
                temperature: 0, // Required field with a fallback value
                  unit: 'celsius',
                error: 'Connection error with medical device service',
                timestamp: Date.now(),
                source: 'MedicalDeviceService'
              };
            });
          }
        });

      console.log('MedicalDeviceService listener registered');
    } catch (error) {
      console.error('Error registering MedicalDeviceService listener:', error);
    }
  }

  /**
   * Safely parse numeric values with fallback
   */
safeParseNumber(value: any, fallback = 0): number {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? fallback : parsed;
}


  /**
   * Safely stringify data for logging
   */
  private stringifyData(data: any): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * Safely stringify errors for logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }
    return String(error);
  }

  async launchThermometer() {
    // this.isReading = true;
    // this.showTemperatureResult = false;
    // this.thermometerData = null;

    try {
      const res = await NovaIcareLauncher.launchDeviceWithResult({
        deviceType: DeviceType.THERMOMETER,
        class_name: 'io.ionic.starter.MainActivity',
        package_name: 'io.ionic.starter',
        language: 'en',
        patient_id: '12345',
        real_id: '',
      });

      if (!res.success) {
        alert('Thermometer launch failed: ' + res.error);
        this.isReading = false;
      }
    } catch (err) {
      alert('Thermometer launch failed: ' + err);
      this.isReading = false;
    }
  }


}
