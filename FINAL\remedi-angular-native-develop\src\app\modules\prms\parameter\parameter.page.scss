/* dashboard.component.scss or .css */
.device-card {
    width: 110px;
    padding: 10px;
    text-align: center;
    border: 2px solid #007bff;
    border-radius: 10px;
    transition: transform 0.2s, background-color 0.2s;
    cursor: pointer;
}

.device-card img {
    width: 50px;
    height: 50px;
    margin-bottom: 6px;
}

.device-card:hover {
    transform: translateY(-5px);
    background-color: #f0f8ff;
}

.device-button {
    padding: 10px 20px;
    background-color: transparent;
    color: #007bff;
    border: 2px solid #007bff;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: 0.3s;
}

.device-button:hover {
    background-color: #e3f2fd;
}
