import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export interface LoginPayload {
  action: string;
  username: string;
  password: string;
  offset: string;
  domainName: string;
  requestFromAngular: string;
  clientId: string;
}

export interface UserResponse {
  status: string;
  token?: string;
  commondetail?: {
    usertype: string;
  };
  profiledetail?: {
    language: string;
  };
  msg?: string;
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly tokenKey = 'authToken';
  private loggedIn$ = new BehaviorSubject<boolean>(this.hasToken());

  constructor(private http: HttpClient, private router: Router) {}

  private hasToken(): boolean {
    return !!localStorage.getItem(this.tokenKey);
  }

  get isLoggedIn(): Observable<boolean> {
    return this.loggedIn$.asObservable();
  }

  login(payload: LoginPayload): Observable<UserResponse> {
    const params = new HttpParams()
      .set('action', payload.action)
      .set('username', payload.username)
      .set('password', payload.password)
      .set('offset', payload.offset ?? '')
      .set('domainName', payload.domainName)
      .set('requestFromAngular', payload.requestFromAngular)
      .set('clientId', payload.clientId);

    const url = environment.APIBaseURLBasic + 'RemediNovaDoctorAPI.do';
    // const url =  'https://s3test2.remedi.co.in/RemediPRMS/RemediNovaDoctorAPI.do';

    return this.http.post<UserResponse>(url, null, { params }).pipe(
      map((res) => {
        if (res.status === 'success' && res.token) {
          localStorage.setItem(this.tokenKey, res.token);
          this.loggedIn$.next(true);
        }
        return res;
      }),
      catchError((error) => {
        console.error('Login failed:', error);
        return throwError(() => error);
      })
    );
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    this.loggedIn$.next(false);
    this.router.navigate(['/login']);
  }
}
