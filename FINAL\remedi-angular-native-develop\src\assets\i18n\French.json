{"0": 0, "1": 1, "2": 2, "3": 3, "Welcome": "Welcome", "Railway_Employee": "Railway Employee", "Prescription": "Ordonnance", "Advise": "Conseil", "Report_Start_Date_Configuration": "Configuration de la date de d�but du rapport", "Report_Start_Date_Updated_Success": "La date de d�but du rapport a �t� mise � jour avec succ�s", "Issue_In_Report_Start_Date_Update": "Probl�me dans la mise � jour de la date de d�but du rapport.", "ob_gyn": "I, ~doctor_name~, declarar que mientras se realiza el <br> evaluaci�n de la imagen de ultrasonido,<br> No he detectado ni revelado el sexo. <br> del feto a cualquiera de cualquier manera.", "please_select": "<PERSON><PERSON><PERSON><PERSON> s�lectionner", "COMPLETE": "<PERSON><PERSON>�<PERSON>", "INCOMPLETE": "Incompl�te", "Change_language_of_all_users_in_this_domain": "Changer la langue de tous les utilisateurs de ce domaine.", "FOLLOWUP": "SUIVRE", "NEW": "NOUVELLE", "Enter_Height_upto_two_decimal_pionts": "Entrez la hauteur correcte.", "Enter_Weight_upto_three_decimal_pionts": "Entrez le poids correct.", "Select_Subchapter": "S�lectionnez un sous-chapitre", "Center_Doctor_Wise_Report": "Center Doctor Wise Report", "Blood_Presure": "Pression art\\uFFFDrielle", "Ultrasound": "Ultrason", "Reconnecting": "Reconnexion ......", "Server_session_has_been_closed_Please_login_again": "Session expir�e. <PERSON><PERSON><PERSON><PERSON> vous reconnecter.", "Please_allow_access_to_Camera_Microphone": "Veuillez autoriser l`acc�s � la cam�ra et au microphone. Puis r�essayez de publier.", "Make_sure_digital_pen_service_is_installed_or_running": "Assurez-vous que le service de stylo num�rique est install� ou en cours d`ex�cution.", "something_went_wrong": "Un probl�me est survenu.", "Mail_Copy_has_been_successfully": "Mail Copy a �t� envoy� avec succ�s.", "Sending_Mail_Copy_Failed": "L`envoi de copie de courrier a �chou�.", "something_went_wrong_when_sending_mail_report": "Un probl�me est survenu lors de l`envoi du rapport.", "Please_upload_files_having_extensions": "S`il vous pla�t t�l�charger des fichiers ayant des extensions", "Taking_Too_Long_To_Print_Retry_Later": "<PERSON><PERSON>re trop longtemps pour imprimer .. R�essayer plus tard", "Print_Success": "Impression r�ussie.", "No_Response_From_Thermal_Printer_Make_Sure_Thermal_Printer_Service_is_Running": "Aucune r�ponse de l`imprimante thermique! Assurez-vous que le service d`imprimante thermique est en cours d`ex�cution.", "Printer_Device_Not_Connected": "P�riph�rique d`impression non connect�.", "Double_Quote_Response_From_Printer": "R�ponse double citation de l`imprimante.", "Blank_Response_From_Printer": "R�ponse vide de l`imprimante.", "No_Response_From_Printer_Make_Sure_Service_is_Running": "Pas de r�ponse de l`imprimante! Assurez-vous que le service est en cours d`ex�cution.", "Invalid_Complaint": "Plainte invalide", "Please_answer_all_questions": "S`il vous pla�t vueillez r�pondre � toutes les questions!", "Data_saved_successfully": "Donn�es sauvegard�es avec succ�s!", "Oops_something_went_wrong": "<PERSON><PERSON>, un probl�me est survenu.", "Please_provide_medicine_name": "Svp, rense<PERSON>ez le nom du m�dicament.", "This_is_not_implemented_yet": "Ceci n`est pas encore impl�ment�", "Recording_Fetal_Data": "Enregistrement de donn�es foetales", "sensor_connected_successfully": "capteur connect� avec succ�s!", "error_while_taking_ECG_reading": "erreur lors de la lecture de l`�lectrocardiogramme", "Problem_while_start_stop_scan": "Probl�me lors du d�marrage / de l`arr�t de l`analyse. Veuillez reconnecter l`appareil et actualiser la page.", "start_scan_status": "<PERSON>�<PERSON><PERSON> l`analyse", "stop_scan_status": "<PERSON><PERSON>�<PERSON> l`analyse", "Your_Image": "Votre image", "Mobile_number": "Num�ro de portable", "Consumables": "Consommables", "Data_Dump": "Data Dump", "All_Doctors_in_this_domain": "Tous les m�decins dans ce domaine", "Please_Switch_On_SpiroSensor_Device": "Veuillez allumer le spirom�tre?", "Please_Switch_On_StethoSensor_Device": "Veuillez allumer le spirom�tre?", "please_select_from_date_first": "veuil<PERSON>z s�lectionner � partir de la date d`abord", "Record_saved_successfully": "Donn�es enregistr�es avec succ�s", "Strip": "Bandelette", "Test_Result": "R�sultat du test", "Reference_Strip": "Bandelette de r�f�rence", "Urine_Test": "Test d`urine", "Reading": "Lecture en cours", "Blood_Pressure": "Tension art�rielle", "Rapid_Reader": "Lecteur rapide", "Test_Image_Not_Found": "Image test introuvable", "Computing_result": "R�sultat informatique", "hrs": "heures", "AND_Test_Code": "ET Code d`essai", "already_exists": "existe d�j�", "Zoom_In": "<PERSON><PERSON><PERSON><PERSON>", "Zoom_Out": "D�zoomer", "Rotate": "<PERSON>e tourner", "Prev": "Pr�c�dent", "Next": "Suivant", "Waiting_for_doctor_to_connect": "En attente de m�decin pour se connecter.", "Connected_with_Doctor": "Connect� avec le docteur.", "Something_went_wrong_please_relogin": "Une erreur est survenue. Reconnectez-vous svp?", "Waiting_for_Patient_to_connect": "En attente de la connexion du patient", "Connected_with_Patient": "Connect� avec le patient.", "Some_error_to_handle": "Une erreur � g�rer", "Optical_Reader_device_Not_Detected": "Lecteur optique non d�tect�", "Result_cannot_be_determined": "Le r�sultat ne peut pas �tre d�termin�", "Slide_not_Present_in_tray": "Diapositive non pr�sente dans le bac", "Directory_not_created": "R�pertoire non cr��", "Could_Not_Open_ROI_Image": "Impossible d`ouvrir l`image ROI", "Invalid_Test": "Test invalide", "Slide_Not_inserted_Properly": "Slide Pas ins�r� correctement", "Test_Type_Not_Implemented": "Type de test non impl�ment�", "Could_Not_Open_Source_Image": "Impossible d`ouvrir l`image source", "Something_Problem_With_Camera": "Une erreur est survenue avec la cam�ra.", "Could_not_open_Camera": "Impossible d`ouvrir la cam�ra", "Device_connected": "Appareil connect�", "Capture": "Capturer", "Error_with_UrineTest_Try_Again": "Erreur avec le test d`urine. R�essayer.", "ERROR_Failed_to_detect_test_strip": "ERREUR", "ERROR_Time_Over_Please_insert_tray_strip_in_lessthan_30seconds": "ERREUR", "UrineTest_Cancelled_Check_connection_camera": "Le test d`urine est annul�. Veuillez v�rifier la connexion de la cam�ra.", "Please_TURN_on_device": "S`il vous pla�t allumer l`appareil", "Please_follow_the_instructions_on_device": "Veuillez suivre les instructions sur l`appareil", "Connect_Device_to_another_port": "Connectez le p�riph�rique � un autre port.", "Device_is_incompitable": "Le dispositif est incompitable", "Record_not_found": "Enregistrement non trouv�.", "ERROR_Failed_to_detect_reference_strip_Please_dont_remove_Tray_or_Strip": "ERREUR", "Caution_For_better_accuracy_retake_reding": "Attention", "Stethoscope_device_connected_successfully": "St�thoscope connect� avec succ�s.", "Make_sure_Stethoscope_Device_is_paired_and_switchedON": "Assurez-vous que le st�thoscope est appari� et allum�.", "Make_sure_Stethoscope_device_is_paired": "Assurez-vous que le st�thoscope est appari�.", "Please_wait_Connecting_Stethoscope_device": "S`il vous pla�t, attendez. Connexion d`un st�thoscope ....", "Enter_Valid_LDL_reading": "Entrez une lecture LDL valide", "Failed_to_Display_Spirometry_Past_Data": "Impossible d`afficher les donn�es ant�rieures de la spirom�trie", "Past_Records_Fetching_Failed": "L`enregistrement des enregistrements pass�s a �chou�", "Fail_to_save_writing_pad_content": "�chec de l`enregistrement du contenu du pav� d`�criture.", "Writingpad_content_successfully_saved": "Le contenu du bloc-notes a �t� enregistr� avec succ�s.", "Offline_Data_Available": "<PERSON><PERSON> hors ligne disponibles", "Makesure_ewritemate_device_is_connected": "Assurez-vous que le p�riph�rique e-Writemate est connect� au syst�me via un c�ble USB.", "WritingPad_Failed_to_load_close_and_open_it_again": "Pav� d`�criture �chec du chargement. Veuillez fermer et l`ouvrir � nouveau.", "image_saved": "image sauvegard�e", "Problem_in_Inserting_Consultation_Fee": "Probl�me lors de l`insertion de frais de consultation", "Problem_in_saving_Consultation_Fee": "Probl�me pour enregistrer les frais de consultation", "Please_provide_only_integer_number": "Veu<PERSON>z ne fournir qu`un nombre entier", "Please_wait_uploading_image": "<PERSON><PERSON><PERSON><PERSON>, t�l�chargement de l`image ....", "Error_while_uploading_image_Please_try_again": "Erreur lors du t�l�chargement de l`image, veuil<PERSON><PERSON> r�essayer!", "Image_uploaded_successfully": "Image t�l�charg�e avec succ�s!", "Invalid_contact_person_lastname": "Nom de la personne � contacter invalide.", "Invalid_contact_person_firstname": "<PERSON><PERSON><PERSON> de la personne � contacter invalide.", "Invalid_ProjectId": "Identifiant de projet invalide.", "Invalid_email": "<PERSON><PERSON> invalide.", "DoubleQuote_Response_From_Printer": "R�ponse double citation de l`imprimante.", "NoResponse_From_ThermalPrinter": "Aucune r�ponse de l`imprimante thermique! Assurez-vous que l`imprimante thermique est en cours d`ex�cution.", "An_unknown_error_occurred_while_trying_to_publish_your_video_Please_try_again": "Une erreur inconnue s`est produite lors de la tentative de publication de votre vid�o. Veuillez r�essayer plus tard.", "is_connected_and_notbeing_used_by_another_application_and_try_again": "est connect� et n`est pas utilis� par une autre application et r�essayez.", "Please_check_that_your_webcam": "S`il vous pla�t v�rifier que votre webcam", "Failed_to_get_access_to_your_camera_or_microphone": "Acc�s � votre cam�ra ou microphone impossible", "Publishing_your_video_failed_This_due_to_restrictive_firewall": "La publication de votre vid�o a �chou�. Cela pourrait �tre d� � un pare-feu restrictif.", "Publishing_your_video_failed": "La publication de votre vid�o a �chou�. Vous n`�tes pas connect� � Internet.", "Your_publisher_lost_its_connection_try_publishing_again": "Connexion perdue.  Veuillez v�rifier votre connexion Internet et essayez � nouveau de publier.", "Media_access_denied": "Acc�s aux m�dias refus�!", "ECG_Unreachable": "Appareil ECG inaccessible. Gardez l`appareil Nereby et recommencez la lecture.", "Provisional": "Provisoire", "Your_Generate_Password_is": "Votre mot de passe g�n�r�e est", "Please_wait": "S`il vous pla�t, attendez", "You_cannot_start_pending_or_review_consultation": "Vous ne pouvez pas commencer en attente ou en consultation d`examen.", "cannot_start_reviewed_consultation": "ne peut pas commencer la consultation revue", "If_Evolute_Service_Utility_is_Running": "Si le service l`utilitaire Evolute est en cours d`ex�cution", "Select_Complaint": "S�lectionnez plainte", "Select_Pharmacy_Name_From_List": "S�lectionner le nom de la pharmacie dans la liste", "Wait_while_Printing": "Attendez! Impression en cours", "Please_Wait_while_Printing": "Veuillez patienter! Impression en cours", "Mobile": "Mobile", "PID": "PID", "record_already_exist": "Dossier existe d�j�.", "Please_Provide_Projectid": "Veuillez renseigner l`dientifiant du projet", "Record_already_exists": "Dossier existe d�j�.", "problem_while_inserting": "probl�me lors de l`insertion", "problem_occured_while_updating": "un probl�me est survenu lors de la mise � jour", "problem_occured_while_inserting_Brand": "un probl�me est survenu lors de l`insertion de la marque", "problem_occured_while_inserting_Drug_Class": "un probl�me est survenu lors de l`insertion de la classe de m�dicaments", "problem_occured_while_inserting_Drug_Form": "un probl�me est survenu lors de l`insertion du formulaire de drogue", "Approve_Number_cannot_be_greater_than_Requested": "Le num�ro d`approbation ne peut pas �tre sup�rieur � celui demand�", "problem_occured_while_inserting_category": "un probl�me est survenu lors de l`insertion de la cat�gorie", "already_exist_Please_Try_another_name": "existe d�j� S`il vous pla�t essayez un autre nom", "problem_occured_while_inserting_lab": "un probl�me est survenu lors de l`insertion du laboratoire", "This_Pharmacy_Detail_already_exists": "Ce d�tail de pharmacie existe d�j�", "Record_is_not_available_to_update": "L`enregistrement n`est pas disponible pour la mise � jour", "Doctor_Record_Not_Found": "Dossier m�dical introuvable", "Doctor_registration_limit_has_been_exceeded": "La limite d`enregistrement du m�decin a �t� d�pass�e", "problem_while_inserting_in_doctorprofile": "probl�me lors de l`insertion dans le profil du m�decin", "problem_while_inserting_in_loginDetails": "probl�me lors de l`insertion dans loginDetails", "problem_occured_while_updating_doctor_profile": "un probl�me est survenu lors de la mise � jour du profil du m�decin", "You_can_not_enable_Doctor_Given_limit_has_been_exceeded": "Vous ne pouvez pas activer la limite de m�decin donn�e a �t� d�pass�e", "Nurse_registration_limit_has_been_exceeded": "La limite d`inscription d`infirmi�re a �t� d�pass�e", "problem_while_inserting_nurse_profile": "probl�me lors de l`insertion du profil de l`infirmi�re", "error_while_storing_nurse_data": "erreur lors de la sauvergarde des donn�es de l`infirmi�re", "You_can_not_enable_Nurse_Given_limit_has_been_exceeded": "Vous ne pouvez activer. Le nombre maximal d`infirmier est atteint.", "problem_while_getting_data": "probl�me survenu lors du recueil des donn�es", "disconnected": "D�connect�.", "completed": "termin�", "failure": "�chec", "suspended": "suspendue", "resumed": "a repris", "failure_to_update": "�chec de la mise � jour", "Cancel": "Annuler", "suspendedbydoctor": "Suspendu par le m�decin", "failure_to_suspend": "Echec suspension", "successfully_sent_this_case_for_review": "Cas envoy� avec succ�s pour exament", "failure_to_send_review": "d�faut d`envoyer un avis", "Please_provide_consultationid_and_reviewby": "Veuillez fournir consultationid et reviewby", "Provide_all_the_details": "<PERSON><PERSON><PERSON> tous les d�tails.", "Error_while_inserting_data": "<PERSON><PERSON>ur lors de l`insertion de donn�es", "Provide_valid_Id": "Donnez un identifiant valide", "Some_slots_are_already_available_in_the_selected_time_range_so_Please_modify_your_selection": "Certains cr�neaux �tant d�j� disponibles dans la plage horaire s�lectionn�e, veuillez modifier votre s�lection.", "slots_already_available": "cr�neaux horaires d�j� disponibles", "error_while_adding_slots": "erreur lors de l`ajout de <PERSON>", "slots_not_found": "emplacements non trouv�s", "success": "Succ�s", "Wrong_Request": "<PERSON><PERSON>u�te incorrecte", "Comment_saved_successfully": "Commentaire enregistr� avec succ�s", "Comment_saving_failed": "�chec de l`enregistrement du commentaire", "failed": "�chou�", "This_medicine_is_already_exist": "Ce m�dicament existe d�j�", "problem_occured_while_inserting_bmq_q1": "un probl�me est survenu lors de l`insertion de bmq q1", "exception_occured_while_inserting_bmq_q1": "une exception s`est produite lors de l`insertion de bmq q1", "problem_occured_while_updating_bmq_q1": "probl�me survenu lors de la mise � jour de bmq q1", "problem_occured_while_deleteing_bmq_q1": "probl�me est survenu lors de la suppression de bmq q1", "problem_occured_while_inserting_bmq_q3": "probl�me est survenu lors de l`insertion de bmq q3", "exception_occured_while_inserting_bmq_q3": "une exception s`est produite lors de l`insertion de bmq q3", "problem_occured_while_updating_bmq_q3": "probl�me survenu lors de la mise � jour de bmq q3", "problem_occured_while_deleteing_bmq_q3": "probl�me est survenu lors de la suppression de bmq q3", "problem_occured_while_inserting_bmq_q2": "un probl�me est survenu lors de l`insertion de bmq q2", "exception_occured_while_inserting_bmq_q2": "une exception s`est produite lors de l`insertion de bmq q2", "problem_occured_while_updating_bmq_q2": "probl�me survenu lors de la mise � jour de bmq q2", "problem_occured_while_deleteing_bmq_q2": "probl�me est survenu lors de la suppression de bmq q2", "problem_occured_while_inserting_complaints": "un probl�me est survenu lors de l`insertion de plaintes", "record(s)_already_exist": "dossier existe d�j�", "problem_occured_while_updating_complaint": "un probl�me est survenu lors de la mise � jour de la plainte", "problem_occured_while_deleteing_complaint": "un probl�me est survenu lors de la suppression de la plainte", "diagnosis_already_prescribed": "diagnostic d�j� prescrit", "ICD_code_already_present": "Code ICD d�j� pr�sent", "problem_occured_while_inserting_diagnosis": "un probl�me est survenu lors de l`insertion du diagnostic", "problem_occured_while_updating_diagnosis": "un probl�me est survenu lors de la mise � jour du diagnostic", "problem_occured_while_deleteing_diagnosis": "un probl�me est survenu lors de la suppression du diagnostic", "problem_occured_while_inserting_recommendations": "un probl�me est survenu lors de l`insertion de recommandations", "problem_occured_while_updating_recommendations": "un probl�me est survenu lors de la mise � jour des recommandations", "problem_occured_while_deleteing_recommendations": "un probl�me est survenu lors de la suppression des recommandations", "problem_occured_while_inserting_invetigation": "probl�me est survenu lors de l`insertion d`une inv�tigation", "test(s)_already_prescribed": "test (s) d�j� prescrit (s)", "problem_occured_while_updating_investigation": "un probl�me est survenu lors de la mise � jour de l`enqu�te", "problem_occured_while_deleting_investigation": "un probl�me est survenu lors de la suppression de l`enqu�te", "Not_Sufficient_Stock_contact_admin": "Stock insuffisant, contactez l`administrateur", "problem_while_inserting_ecg_data_into_master_table": "probl�me lors de l`insertion des donn�es de l`�lectrocardiogramme dans la table principale", "problem_while_inserting_ecg_filename_into_database": "probl�me lors de l`insertion du nom de fichier l`�lectrocardiogramme dans la base de donn�es", "problem_while_inserting_stetho_data_into_master_table": "probl�me lors de l`insertion de donn�es st�tho�des dans la table principale", "problem_while_inserting_stetho_filename_into_database": "probl�me lors de l`insertion du nom de fichier stetho dans la base de donn�es", "problem_while_inserting_spo2_filename_into_database": "probl�me lors de l`insertion du nom de fichier spo2 dans la base de donn�es", "problem_while_inserting_spiro_data_into_master_table": "probl�me lors de l`insertion des donn�es spiro dans la table principale", "problem_while_inserting_fetal_data_into_master_table": "probl�me lors de l`insertion de donn�es foetales dans la table principale", "problem_while_inserting_fetal_filename_into_database": "probl�me lors de l`insertion du nom de fichier foetal dans la base de donn�es", "problem_while_RETRIVING_MAX_FROM_FETAL_filename_into_database": "probl�me en r�cup�rant le nom de fichier MAX DE FETAL dans la base de donn�es", "problem_while_RETRIVING_MAX_FROM_stetho_filename_into_database": "probl�me survenu lors de la r�cup�ration du fichier  MAX de stetho dans la base de donn�es", "problem_while_RETRIVING_MAX_FROM_ECG_BLE_filename_into_database": "probl�me survenu en r�cup�rant le fichier MAX de ECG BLE  dans la base de donn�es", "problem_while_RETRIVING_MAX_FROM_ECG_filename_into_database": "probl�me survenu lors de la r�cup�ration du fichier MAX de ECG dans la base de donn�es", "problem_while_RETRIVING_MAX_FROM_spo2_filename_into_database": "probl�me survenu lors de la r�cup�ration du fichier MAX de spo2 dans la base de donn�es", "problem_while_inserting_patient_monitor_data_into_master_table": "probl�me survenu lors de l`insertion des donn�es du moniteur patient dans la table principale", "problem_while_inserting_patient_Monit_FileName_into_database": "probl�me lors de l`insertion du patient Monit FileName dans la base de donn�es", "saved": "enregistr�e", "problem_occured_while_inserting_referral": "un probl�me est survenu lors de l`insertion d`une r�f�rence", "problem_occured_while_updating_referrals": "un probl�me est survenu lors de la mise � jour des r�f�rences", "problem_occured_while_deleting_referrals": "un probl�me est survenu lors de la suppression des r�f�rences", "Tests": "<PERSON><PERSON><PERSON>", "Can't_find_dependent_libraries": "Impossible de trouver des biblioth�ques d�pendantes!", "Finger_does_not_matched": "Le doigt ne correspond pas!", "An_error_occurred_while_loading_device_libraries": "Une erreur s`est produite lors du chargement des biblioth�ques de p�riph�riques.", "Place_your_finger_properly": "Placez votre doigt correctement", "No_error": "Pas d`erreur", "JSGFPLib_object_creation_failed": "La cr�ation d`objet JSGFPLib a �chou�", "Function_call_failed": "L`appel de fonction a �chou�", "Invalid_parameter_used": "Param�tre non valide utilis�", "Not_used_function": "Fonction non utilis�e", "DLL_loading_failed": "�chec du chargement de la DLL", "Device_driver_loading_failed": "Echec du chargement du pilote de p�riph�rique", "Algorithm_DLL_loading_failed": "�chec du chargement de la DLL de l`algorithme", "Cannot_find_driver_sys_file": "Impossible de trouver le fichier syst�me du pilote", "Chip_initialization_failed": "L`initialisation de la puce a �chou�", "Image_data_lost": "<PERSON><PERSON> d`image perdues", "Image_capture_timeout": "<PERSON>�<PERSON> de capture d`image", "Device_not_found": "Appareil non trouv�", "Driver_file_load_failed": "�chec du chargement du fichier de pilote", "Wrong_image": "Mauvaise image", "Lack_of_USB_bandwidth": "Manque de bande passante USB", "Device_is_already_opened": "L`appareil est d�j� ouvert", "Serial_number_does_not_exist": "Le num�ro de s�rie n`existe pas", "Unsupported_device_Extract_&_Matching_Error_Codes": "Extraction et correspondance des codes d`erreur de p�riph�rique non pris en charge", "Inadequate_number_of_minutiae": "Nombre insuffisant de minuties", "Wrong_template_type": "Mauvais type de mod�le", "Error_in_decoding_template_1": "Erreur de d�codage du mod�le 1", "Error_in_decoding_template_2": "Erreur de d�codage du mod�le 2", "Extraction_failed": "L`extraction a �chou�", "Matching_failed": "La correspondance a �chou�", "Undefined_error_condition": "Condition d`erreur non d�finie!", "Place_finger_on_device": "Placez le doigt sur l`appareil!", "Finger_print_captured_successfully": "Empreinte digitale enregistr�e avec succ�s!", "Error_while_parsing_finger_print_data": "Erreur lors de l`analyse des donn�es d`empreinte digitale!", "Error_while_register_finger_print": "Erreur lors de l`enregistrement des empreintes digitales!", "Please_place_registered_finger_on_device": "Veu<PERSON><PERSON> placer le doigt enregistr� sur l`appareil!", "Error_while_verifying_finger_print": "Erreur lors de la v�rification des empreintes digitales!", "Finger_print_not_registered": "Empreinte digitale non enregistr�e!", "View_Report": "Voir le rapport", "Start_Debugging": "Lancer le d�bogage", "Stop_Debugging": "<PERSON><PERSON>�<PERSON> le d�bogage", "Loading_Camera": "Chargement de la cam�ra", "Camera": "Cam�ra", "Retry": "R�essayez", "Take_snapshot": "<PERSON><PERSON><PERSON> un instantan�", "Retake": "Reprendre", "Please_connect_camera": "Connectez l`appareil photo svp", "Please_allow_access_to_camera": "Veuillez autoriser l`acc�s � la cam�ra", "Make_sure_camera_is_connected_and_accessible": "Assurez-vous que la cam�ra est connect�e et accessible", "Initializing": "Initialisation.....", "PleaseWait": "S`il vous pla�t, attendez.....", "Tray_with_cassette_is_inserted_properly": "Le bac avec cassette est ins�r� correctement.", "Makesure_device_connected_and_switched_ON": "Assurez-vous que l`appareil est connect� et allum�.", "error_while_taking_BP": "erreur en prenant BP.", "Measurement_Error": "<PERSON><PERSON><PERSON> de mesure", "Pulse_rate_Error": "<PERSON><PERSON><PERSON> de fr�quence cardiaque", "Too_low_BP_(beyond_range)": "BP trop bas (au-del� de la plage)", "Very_high_BP_(beyond_range)": "BP tr�s �lev� (au-del� de la plage)", "Overpressure_Error_The_cuff_pressure_exceeded_280mmHg": "Erreur de surpression. La pression du brassard a d�pass� 280 mmHg", "Deflation_Error": "Erreur de d�flation", "Inflation_Error": "Erreur d`inflation", "Cuff_is_not_connected": "Le brassard n`est pas connect�", "Lead_not_connected": "Fil (s) non connect� (s). <br> S`il vous pla�t connecter tous les fils correctement et reprendre la lecture.", "StethoScope": "St�thoscope", "Error_while_saving_data": "<PERSON><PERSON>ur lors de la sauvegarde des donn�es.", "Waiting_for_Sensor_to_Connect": "En attente de la connexion du capteur", "Otoscope": "Otoscope", "Please_SwitchOn_StethoSensor_Device": "Veuillez allumer le p�riph�rique StethoSensor.", "Please_take_proper_reading": "S`il vous pla�t prendre une bonne lecture", "Enter_Valid_Glucose_percentage": "Entrez un pourcentage de glucose valide", "Gain_Setting": "R�glage du gain", "Patient_Monitor": "<PERSON><PERSON><PERSON>", "Till_Minute": "<PERSON><PERSON>q<PERSON>`� la minute", "Till_Hour": "<PERSON><PERSON>q<PERSON>`� l`heure", "From_Hour": "De l`heure", "From_Minute": "� la minute", "Dispensed": "Distribu�", "Stock": "Stock", "Expiry": "Expiration", "Batch": "Lot", "Serial_No": "Sr.No", "Stock_Information": "Information sur le stock", "Dose": "<PERSON><PERSON>", "No_Of_Days": "Jours", "Stock_updated_successfully": "Stock mis � jour avec succ�s!", "Error_while_updating_stock": "Erreur lors de la mise � jour du stock!", "Please_update_stock": "Veuillez mettre � jour le stock!", "freetext_entry_OR_Not_in_inventory": "Peut �tre une entr�e de texte libre OU pas dans l`inventaire!", "only": "seulement", "Interventions_uploaded_successfully": "Les interventions ont �t� t�l�charg�es avec succ�s", "Complaints_uploaded_successfully": "Les plaintes ont �t� t�l�charg�es avec succ�s", "Recommendations_uploaded_successfully": "Les recommandations ont �t� t�l�charg�es avec succ�s", "There_was_an_error": "Il y avait une erreur", "Request_doesnot_contain_uploaddata": "La demande ne contient pas de donn�es de t�l�chargement", "TestIntervention_uploaded_successfully": "TestIntervention a �t� charg� avec succ�s", "Medicines_uploaded_successfully": "Les m�dicaments ont �t� t�l�charg�s avec succ�s", "Uploaded_excelformat_not_correct_duplicaterow_entry": "Le format Excel t�l�charg� n`est pas correct ou duplique l`entr�e de la ligne", "Please_upload_file": "S`il vous pla�t t�l�charger le fichier", "Reduce_Stock": "<PERSON><PERSON><PERSON> le stock", "Add_Stock": "Ajouter un stock", "No_data_available": "Pas de donn�es disponibles !", "Fee": "<PERSON><PERSON>", "Parameter_Price": "Param�tre Price", "Approve_Paid_Coupons": "Approuver les coupons pay�s", "Available_Free_Coupons": "Coupons gratuits disponibles", "Available_Paid_Coupons": "Coupons pay�s disponibles", "Requested_Paid_Coupons": "Coupons pay�s demand�s", "Nurse_Username": "Nom d`utilisateur infirmi�re", "Select_District": "Choisissez un district", "Select_State": "S�lectionnez l`�tat", "Select_Country": "Choisissez le pays", "Select_Category": "Choisir une cat�gorie", "Select_Form": "S�lectionner le formulaire", "Select_Class": "S�lectionnez une classe", "Select_Brand": "S�lectionnez une marque", "Your_followup_discount_added_successfully": "Votre remise de suivi a �t� ajout�e avec succ�s.", "please_enter_followup_discount": "ve<PERSON><PERSON><PERSON> saisir la remise de suivi", "please_select_first_username": "<PERSON><PERSON><PERSON><PERSON> s�lectionner d`abord le nom d`utilisateur", "Capture_Finger_Print": "Enregistrement d`empreintes digitales", "Connect": "Re<PERSON>", "record_not_found": "Dossier non trouv�.", "device_is_not_reachable": "L`appareil n`est pas accessible", "Problem_generate_new_password": "Probl�me pour g�n�rer un nouveau mot de passe", "Select_DateFormat": "S�lectionner le format de la date", "Select_Speciality": "Choisissez une sp�cialit�", "Display_Date_Format": "Format de date d`affichage", "Download_Stock_Template": "T�l�charger le mod�le de stock", "download_test_intervention_template": "t�l�charger le mod�le d`intervention de test", "Not_Generated": "Non g�n�r�", "Generated_Successfully": "G�n�r� avec succ�s", "Available": "Disponible", "Out_of_stock": "En rupture de stock", "ExpiryDate": "Date d`expiration", "Batch_Number": "<PERSON><PERSON>�<PERSON>", "Available_Stock": "Stock disponible", "Medicine_Name": "Nom du m�dicament", "Please_enter_followup_discount_in_percentage": "Veuillez saisir la remise de suivi en%", "Select_Doctor": "Choisissez un docteur", "Select_Nurse": "S�lectionnez l`infirmi�re", "Upload_File_Size_lessthan_2Mb": "La taille du fichier de t�l�chargement doit �tre inf�rieure � 2 Mo.", "Price": "Prix", "Test": "Tester", "Select_Test": "S�lectionnez le test", "Select_LabName": "S�lectionnez le laboratoire", "Disabled": "d�sactiv�", "Enabled": "Activ�e", "ACR_ManualEntry": "<PERSON><PERSON> manuelle ACR", "Select_Result": "S�lectionnez le r�sultat", "select_recommendations": "s�lectionner des recommandations", "UrineTest_Results_Saved_Successfully": "R�sultats des tests d`urine enregistr�s avec succ�s.", "Please_Initialize_again": "Veuillez r�initialiser � nouveau.", "Please_connect_the_device": "Veuillez connecter le p�riph�rique et assurez-vous que le plateau avec la bande de r�f�rence est ins�r� correctement.", "Urine_Note": "<PERSON><PERSON><PERSON>", "Insert_tray_with_prepared_teststrip_in_device": "Ins�rez le plateau avec la bandelette r�active pr�par�e dans le p�riph�rique.", "Place_teststrip_on_tray_proper_position": "Placez la bandelette r�active sur le plateau au bon endroit", "Remove_excess_urine": "Enlevez l`exc�s d`urine", "Click_startbutton_when_removing_teststrip_from_urinesample": "Cliquez sur le bouton de d�marrage exactement au moment o� vous retirez la bandelette d`analyse de l`�chantillon d`urine", "Dip_teststrip_in_Urinesample": "Bandelette r�active dans l`�chantillon d`urine", "Computing_Result": "R�sultat informatique", "HBA1C_ManualEntry": "HBA1C Entr�e manuelle", "Please_SwitchOn_FetalSensor_Device": "Veuillez allumer l`appareil foetal sensor.", "Spirometry": "Spi<PERSON>�<PERSON>e", "Please_SwitchOn_SpiroSensor_Device": "Veuillez allumer le spirom�tre", "SpiroMeter_ManualEntry": "<PERSON><PERSON> man<PERSON>le s<PERSON>", "LipidProfile": "Profile lipidique", "Lipid_ManualEntry": "Entr�e manuelle lipidique", "UrineTest_ManualEntry": "<PERSON><PERSON> manuelle du test d`urine", "RapidTest_ManualEntry": "<PERSON><PERSON> manuelle du test rapide", "Glucose_ManualEntry": "<PERSON><PERSON> man<PERSON> du <PERSON>", "Please_SwitchOn_Spo2Sensor": "Veuillez allumer le p�riph�rique Spo2Sensor.", "BloodPressure_ManualEntry": "<PERSON><PERSON> man<PERSON> de la pression art�rielle", "Please_SwitchOn_BPSensorDevice": "Veuillez allumer le p�riph�rique BPSensor.", "Please_SwitchOn_ECGSensorDevice": "Veuillez allumer le dispositif ECGSensor.", "PulseOximeter_ManualEntry": "<PERSON><PERSON> manuelle oxym�tre", "Please_Switch_On_Temperature_Device": "Veuillez allumer le thermom�tre", "Temperature_Manual_Entry": "<PERSON><PERSON> man<PERSON> de la temp�rature", "Failed_to_save_UrineTest_ManualEntrydata": "�chec de l`enregistrement des donn�es de saisie manuelle du test d`urine.", "Successfully_saved_Urine_Test_Manual_Entry_data": "Enregistrement r�ussi des donn�es de saisie manuelle du test d`urine.", "Please_proper_GLU_value_format": "S`il vous pla�t bon format de valeur GLU", "Please_proper_KET_value_format": "S`il vous pla�t bon format de valeur KET", "Please_proper_BLO_value_format": "S`il vous pla�t bon format de valeur BLO", "Please_proper_PRO_value_format": "S`il vous pla�t bon format de valeur PRO", "Please_proper_LEU_value_format": "S`il vous pla�t bon format de valeur LEU", "Please_select_NIT_test_result": "Veuillez s�lectionner le r�sultat du test NIT dans la liste d�roulante", "Failed_to_save": "�chec de la sauvegarde", "data": "<PERSON> donn�es.", "Successfully_saved": "Enregistr� avec succ�s", "Please_select_testresult_from_dropdown": "Veuillez s�lectionner le r�sultat du test dans la liste d�roulante", "Please_select_TestType_from_dropdown": "Veuillez s�lectionner le type de test dans la liste d�roulante", "Please_wait_fetching_result_from_device": "<PERSON><PERSON><PERSON><PERSON> patienter, nous r�cup�rons le r�sultat de l`appareil!", "Failed_to_save_Fetal_data": "�chec de la sauvegarde des donn�es foetales", "Successfully_saved_Fetal_data": "Donn�es foetales enregistr�es avec succ�s.", "Enter_Valid_HeartRate_Reading": "Entrez un taux de fr�quence cardiaque valide", "Result_fetched_successfully": "R�sultat r�cup�r� avec succ�s!", "Error_while_saving_Hba1c_data": "Erreur lors de la sauvegarde des donn�es Hba1c.", "Successfully_saved_Hba1c_data": "Sauvegard� avec succ�s des donn�es Hba1c.", "Please_enter_hemoglobin_percentage": "Veuillez entrer le taux d`h�moglobine%", "Successfully_saved_Lipid_data": "Donn�es de lipides sauvegard�es avec succ�s.", "Enter_Valid_Triglycerides_Cholestrol_reading": "Entrez le relev� de triglyc�rides valides dans le cholest�rol", "Enter_Valid_HDL_reading": "Entrez une valeur HDL valide", "Enter_Valid_Total_Cholestrol_reading": "Entrez une valeur de cholestrol valide", "Successfully_saved_Spirometer_data": "Donn�es du spirom�tre sauvegard�es avec succ�s.", "Enter_Valid_TLC_rate": "Entrer un taux de TLC valide", "Enter_Valid_TV_rate": "Entrez un taux de  TV valide", "Enter_Valid_PEF_rate": "Entrez un taux de PEF valide", "Enter_Valid_FEF75_rate": "Entrez un taux de validit� FEF75", "Enter_Valid_FEF50_rate": "Entrez un taux de validit� FEF50", "Enter_Valid_FEF25_rate": "Entrez un taux de validit� FEF25", "Enter_Valid_FEV_Percentage_rate": "Entrer un pourcentage de VEM valide", "Enter_Valid_FEV1_reading": "Entrer une valeur du FEV1 valide", "Enter_Valid_FVC_reading": "Entrez une lvaleur de FVC valide", "Successfully_saved_bloodpressure_data": "Donn�es de pression art�rielle sauvegard�es avec succ�s.", "Enter_Valid_diastolic_reading": "Entrez une valeur diastolique valide", "Enter_Valid_systolic_reading": "Entrez une valeur systolique valide", "Successfully_saved_ACR_data": "Donn�es ACR sauvegard�es avec succ�s.", "Triglycerides": "Triglyc�rides", "Successfully_saved_Temperature_data": "Donn�es de temp�rature enregistr�es avec succ�s.", "Enter_Valid_entryof_fahrenheit": "Entrez une valeur valide en fahrenheit", "Successfully_saved_Spo2_data": "Donn�es CO2 sauvegard�es avec succ�s.", "Values_cannot_be_empty": "Les valeurs ne peuvent pas �tre vides.", "Enter_Valid_pulse": "Entrez un pouls valide", "Enter_Valid_oxygen_percentage": "Entrez un pourcentage d`oxyg�ne valide", "Successfully_saved_Glucose_data": "Donn�es de glucose enregistr�es avec succ�s.", "Enter_Valid_Glucose_Value": "Entrez une valeur de glucose valide", "Fields_cannot_be_empty": "Les champs ne peuvent pas �tre seulement des points ou laiss�s en blanc.", "Please_select_test": "S`il vous pla�t s�lectionnez le test!", "Please_take_reading_again": "S`il vous pla�t prendre � nouveau la valeur", "After_beepsound_please_Waitfor_2sec_tostart_reading": "Apr�s le bip, attendez 2 secondes avant de commencer � lire.", "Reading_improper_please_take_again": "La valeur n`est pas prise correctement. S`il vous pla�t prendre � nouveau.", "no_manual_entry_param": "pas de param�tre de saisie manuelle", "HDL_Cholesterol": "HDL Cholest�rol", "Total_Cholesterol": "Cholest�rol total", "GLUCOSE": "GLUCOSE", "BILIRUBIN": "BILIRUBIN", "SPECIFIC_GRAVITY": "GRAVIT� SP�CIFIQUE", "BLOOD": "DU SANG", "PROTEIN": "PROT�INE", "UROBILINOGEN": "UROBILINOGEN", "NITRITE": "NITRITE", "LEUKOCYTES": "LEUCOCYTES", "ReferenceStrip": "Bandelette de r�f�rence", "TestStrip": "Bandelette de test", "Result": "R�sultat", "Processing": "En traitement", "Initialize": "Initialiser", "Click_the_Initialize": "Cliquez sur le bouton Initialiser", "Check_referencestrip_in_tray": "V�rifier la pr�sence de bandelette de r�f�rence dans le bac", "Negative": "<PERSON><PERSON><PERSON>", "Positive": "<PERSON><PERSON><PERSON><PERSON>", "Click_the_Start_Test_button": "Cliquez sur le bouton D�marrer le test", "Select_Test_from_dropdown": "S�lectionnez le test dans la liste d�roulante", "Switch_on_device": "<PERSON><PERSON>z l`appareil", "Insert_tray_in_optical_device": "Ins�rer un plateau dans le lecteur optique", "Connect_optical_reader": "Connectez le lecteur optique � un ordinateur � l`aide d`un c�ble USB", "Please_follow_steps": "Veuillez suivre les �tapes ci-dessous", "AmplitudeBar": "Barre d`amplitude", "Note": "<PERSON><PERSON><PERSON>", "Remedi2": {"0_not_running_refresh_page": "Le composant interne de Remedi2.0 n`est pas en cours d`ex�cution. Veuillez raffra�chir la page."}, "SaveResults": "Enregistrer les r�sultats", "Start": "Commencer", "StartTest": "Lancer le test", "HeartRate": "<PERSON><PERSON><PERSON><PERSON> cardia<PERSON>", "Record": "Enregistrer", "Discard": "Eliminer", "Next_Reading": "Lecture suivante", "Consultation_unmarked_as_followup": "Consultation non marqu�e comme suivi.", "Consultation_marked_as_followup": "Consultation marqu�e comme suivi.", "Blood_And_Urine_Tests": "Tests de sang et d`urine", "Foetal_Care": "Soins foetaux", "ACR_Test": "Test ACR", "HbA1c": "HbA1c", "Lipid_Profile": "Profile lipidique", "Physiology_Tests": "Tests physiologiques", "Ok": "d'accord", "Problem_in_Changing_Password": "Probl�me de changement de mot de passe.", "Old_Password_is_incorrect": "Ancien mot de passe incorrect", "Password_has_been_changed_successfully": "Le mot de passe a �t� chang� avec succ�s.", "Please_check_the_terms_and_conditions": "S`il vous pla�t v�rifier les termes et conditions.", "terms_and_conditions": "les conditions g�n�rales d`utilisation", "I_agree_with": "J`approuve", "Patient_already_in_consultation_with_some_other_user": "Patient  d�j� en consultation sous le nom d`un autre utilisateur", "edit": "Editer", "Search_By_Finger_Print": "Recherche par empreinte digitale", "Mail_Report": "Rapport de courrier", "Thermal_Printer": "Imprimante thermique", "Print_Report": "Imprimer le rapport", "Medicines_not_precribed_in_this_consultation": "M�dicaments non prescrits lors de cette consultation", "Patient_Delivery_Address": "<PERSON><PERSON><PERSON> <PERSON> liv<PERSON><PERSON> du patient", "Phone_Number": "Num�ro de t�l�phone", "Please_Enter_The_Phone_Number": "S`il vous pla�t entrer le num�ro de t�l�phone", "Please_Select_The_Pharmacy_Name": "Veuillez s�lectionner le nom de la pharmacie", "Print_in_Thermal_Printer": "Imprimer dans une imprimante thermique", "Print": "<PERSON><PERSON><PERSON><PERSON>", "Test_Name": "Nom du test", "Units": "<PERSON><PERSON>", "Results": "R�sultats", "Reference_Value_Range": "<PERSON><PERSON> de valeurs de r�f�rences", "Invoice": "Facture", "Description": "Description", "Parameter": "<PERSON><PERSON>�<PERSON>", "Sr": {"No": {"": "Sr.No."}}, "This_is_an_electronically_generated_invoice": "Ceci est une facture g�n�r�e �lectroniquement", "Additional_Notes": "Notes compl�mentaires", "Test_fees": "Frais de test", "Consultation_Fee": "Frais de consultation", "Total": "Total", "years": "<PERSON>", "months": "<PERSON><PERSON>", "days": "Jour", "Capture_finger_print": "�Prendre une empreinte digitale", "OR": "OU", "Consultation_Fee_Saved_Successfully": "Frais de consultation sauvegard�s avec succ�s", "Please_select_nurse": "Veuillez s�lectionner infirmi�re", "please_select_chapter": "<PERSON><PERSON><PERSON><PERSON> s�lectionner un chapitre", "Please_select_diagnosis_category": "<PERSON><PERSON><PERSON>z s�lectionner une cat�gorie de diagnostic", "Please_select_subchapter": "Veuillez s�lectionner un sous-chapitre", "Select_Referral": "S�lectionnez une r�f�rence", "Select_Recommendations": "S�lectionnez des recommandations", "Select_Lab": "S�lectionner un laboratoire", "Select_Instruction": "Selectionnez instruction", "Select_Medicine": "Selectionnez m�dicament", "Select_Diagnosis": "Selectionnez diagnostic", "Select_Chapter": "Selectionnez chapitre", "Recent_Diagnosis": "Diagnostic r�cent", "Click_On_Diagnosis_Name_To_Add": "Cliquez sur le nom du diagnostic sans ajouter", "Recent_Complaints": "R�centes plaintes", "click_on_complaint_name_to_add": "Cliquer sur l`intitul� de la plainte � ajouter", "Since": "<PERSON><PERSON><PERSON>", "Complaint_Testing": "Test de plainte", "Recently_Prescribed": "R�cemment prescrit", "click_on_medicine_name_to_add": "Cliquez sur le nom du m�dicament � ajouter", "click_on_investigations_name_to_add": "cliquez sur le nom des enqu�tes � ajouter", "Recent_investigations": "Enqu�tes r�centes", "click_on_instructions_name_to_add": "cliquez sur le nom des instructions � ajouter", "Recent_instructions": "Instructions r�centes", "click_on_referral_name_to_add": "cliquez sur le nom de la r�f�rence  � ajouter", "Recently_Referred": "R�cemment r�f�r�", "Hours": "<PERSON><PERSON>", "Days": "Jour", "Weeks": "Se<PERSON>ines", "Months": "<PERSON><PERSON>", "Years": "<PERSON>", "Chapter": "Chapitre", "Subchapter": "Sous-chapitre", "Chapter_wise": "<PERSON><PERSON><PERSON> sage", "Code": "Code", "Upload_Domain_Logo": "T�l�charger le logo du domaine", "Parameter_Fee_Configuration": "Param�tre Configuration des frais", "Parameter_Configuration": "Configuration des param�tres", "Follow_Up": "Suivre", "Medicine_Stock": "Stock de m�dicaments", "Test_Fee_Configuration": "Configuration des frais de test", "Upload_Complaints": "T�l�charger les plaintes", "Download_Complaints_Template": "t�l�charger le mod�le de r�clamation", "Upload": "T�l�charger", "Close": "<PERSON><PERSON><PERSON>", "Generate_Password": "Cr�er un mot de passe", "Reset_Password": "r�initialiser le mot de passe", "Upload_Medicine": "T�l�charger des m�dicaments", "Download_Medicine_Template": "T�l�charger le mod�le de m�decine", "Upload_Recommendations": "T�l�charger des recommandations", "Add_Logo": "Ajouter un logo", "Add_Hospital_Name": "Ajouter un nom d`h�pital", "Follow-up_Discount": "Actualisation du suivi", "Save": "sauve<PERSON><PERSON>", "Upload_Intervention": "T�l�charger l`intervention", "Download_Intervention_Template": "T�l�charger le mod�le d`intervention", "Download_Recommondations_Template": "T�l�charger le mod�le de recommandations", "Nurse": "Infirmi�re", "Save_Discount": "Enregistrer la remise", "Upload_Medicine_Stock": "T�l�charger un stock de m�dicaments", "Upload_TestIntervention": "T�l�charger le test d`intervention", "Generate_TestPrices_Template": "G�n�rer un mod�le de prix de tests", "Country": "Pays", "Generate_Stock_Template": "G�n�rer un mod�le de stock", "chief_complaints": "<PERSON><PERSON> plaintes", "please_enter_chief_complaint": "Veuillez saisir la plainte principale", "data_saved_successfully": "Donn�es sauvegard�es avec succ�s!", "problem_while_saving_chief_complaint": "Probl�me lors de l`enregistrement de la plainte principale", "Select_User": "S�lectionnez un utilisateur", "Enter_Patient_Id": "Entrer l`ID du patient", "Local_Consultation": "Consultation locale", "Remote_Consultation": "Consultation � distance", "Review_Cases": "Examiner les cas", "Slots_have_been_added_successfully": "Les emplacements ont �t� ajout�s avec succ�s.", "Pharmacy_Configuration": "Configuration de pharmacie", "Pharmacy": "pharmacie", "Please_Enter_Quantity": "S`il vous pla�t fournir la quantit�", "Quantity": "quantit�", "ProjectId": "ID du projet", "Username": "nom d`utilisateur", "Password": "Mot de passe", "Login": "Se connecter", "Neurosynaptic": "Copyright", "nurseLogin": "Connexion de l`infirmi�re", "Hello": "Bonjour�", "Make_Appointments": "Prise de rendez-vous", "Register_New_Patient": "Enregistrer un nouveau patient", "Or_Fix_Appointment": "Fixer un rendez-vous", "Choose_speciality": "Choisir une sp�cialit�", "Fee_Range": "<PERSON><PERSON><PERSON> de frais", "Time_Range": "Intervalle de temps", "Find_Doctor": "Trouver un docteur", "Diagnostic_or_Patient_Records": "Diagnostic ou dossiers patients", "Or_Search_Patient": "Recherche Avanc�e", "Or_Register_Patient": "Inscrire un patient", "Go_For_Diagnostic": "<PERSON><PERSON><PERSON> un diagnostic�", "Go_For_Print_Patient_ID_Card": "Imprimer la fiche patient", "View_Patient_Records": "Dossiers patients", "Show_Appointments": "<PERSON><PERSON> les rendez-vous", "Your_Appointments": "<PERSON><PERSON><PERSON>vous", "Patient_Image": "Photo Patient", "Patient_Name": "Nom Pat<PERSON>", "Start_Time": "<PERSON><PERSON> de d�<PERSON>", "End_Time": "Heure de fin", "Appointment_Date": "Date rendez-vous", "Nurse_Name": "Infirmi�re Nom", "Status": "Etat", "Action": "Statut�", "Parameter_Manual_Entry_Status": "Entr�e manuelle des param�tres", "Please_Provide_Patient_Id_first": "S`il vous pla�t fournir l`identit� du patient", "Please_Provide__Valid_Patient_Id": "Veuillez fournir un num�ro de patient valide.", "Patient_is_not_registered": "Le patient n`est pas enregistr� svp  faire des investigations", "No_Patient_Found": "Aucun patient Trouv�", "No_Appointment_Found": "Aucun rendez-vous Trouv�", "Active": "actif", "Cancelled": "<PERSON><PERSON>�", "Completed": "termin�", "Both_Active_and_Cancelled": "A la fois actif et annul�", "Suspended": "suspendu", "All": "<PERSON>ut", "Pending": "En attente", "Reviewed": "Examin�", "Search_Result": "R�sultat de recherche", "Id": "ID", "Gender": "<PERSON>e", "Date_of_Birth": "Date de naissance", "Search_Doctor": "Chercher un m�decin", "Fix_Appointment": "Fixer un rendez-vous", "Book": "livre", "Doctor_Name": "Nom Docteur", "Degree": "<PERSON><PERSON><PERSON><PERSON>", "Available_Slots": "Emplacements disponibles", "From_Time": "De", "To_Time": "A", "Cancel_the_slots": "Annuler les cr�naux", "Book_the_slots": "R�server le cr�neau", "Please_Select_Doctor_Name_First": "S`il vous pla�t S�lectionnez le pr�nom du m�decin", "Please_Select_Current_Date_First": "SVP selectionner la date  d`aujourd`hui", "You_cannot_select_the_date_less_than_current_date": "Vous ne pouvez selectionner que la date d`aujourd`hui", "Please_Choose_slot_first": "SVP choisir le cr�neau d`abord", "Slot_has_already_been_taken_by_someone_else": "Le cr�neau a d�j� �t� pris par quelqu`un d`autre", "No_Slot_Available": "Les cr�neaux ne sont pas disponibles pour le m�decin choisi", "Something_went_wrong_Please_retry": "Quelque chose ne marche pas bien svp r�essayer", "something_went_to_wrong_please_relogin": "Quelque chose ne marche pas bien svp  se  connecter � nouveau", "Problem_in_starting_local_consultation": "Probl�me au d�marrage consultation locale .", "You_cannot_start_cancelled_appointment": "Vous ne pouvez pas commencer rendez-vous annul�", "OK": "Ok", "search_Patient": "Rechercher un patient", "Register_By_You": "Enregistrer par vous", "Register_By_All": "Enregistrer par tous", "From_Date": "A partir de la date", "Till_Date": "Jusqu`� la date", "patient_first_Name": "<PERSON><PERSON>�<PERSON>", "patient_Last_Name": "<PERSON><PERSON>�<PERSON>", "DOB": "DOB", "Male": "<PERSON><PERSON>", "Female": "<PERSON>mme", "Other": "Transgenre", "Age": "�ge", "Address": "<PERSON><PERSON><PERSON>", "Search": "Recherche", "Additional_Information": "Information suppl�mentaire", "Please_Provide_Patient_First_Name_to_Search": "SVP fournir le pr�nom du patient � rechercher", "Mobile_contact_number": "Num�ro de t�l�phone / contact", "Married": "<PERSON>�(e)", "Unmarried": "Celibataire", "Hieght": "<PERSON><PERSON>", "Weight": "Poids", "Head_of_Household": "P�re/Tuteur", "Email": "Email", "Head_of_HouseHold_first_name": "contact personne Premier <PERSON>m", "Head_of_HouseHold_Last_name": "<PERSON><PERSON><PERSON> la personne � contacter", "UID": "UID", "Past_Records": "dossiers pass�es", "Diagnosis": "Diagnostic", "Update_Patient": "Mise � jour donn�es patient", "Register_Patient": "registre des patients", "Select_country": "Choisissez le pays", "Select_Block": "<PERSON>ner le bloc", "Select_Village": "S�lectionnez Village", "Upload_Patient_Image": "<PERSON><PERSON><PERSON> une photo du patient", "Take_another": "prenez un autre", "Please_Select_Country_First": "SVP selectionner d`abord le pays", "Please_Select_State_First": "SVP selectionnez d`abord l`Etat", "Please_Select_District_First": "SVP selectionnez d`abord le district", "Please_Select_location_in_order": "SVP selectionnez les lieux en ordre", "Upload_Reports": "T�l�charger le document / Image", "Upload_from_computer": "Consulter l`ordinateur pour l`image", "Take_live_photo": "capture d` image", "Image_Type": "Type de l`image", "Document_Type": "Type de document", "Take_Another": "prenez un autre", "Register1": "Enregistrer", "please_share_camera_first_and_then_take_photo": "SVP partager la camera pour prendre une photo", "please_Browse_record_from_computer_OR_take_from_camera": "SVP consulter l`ordinateur pour trouver une image ou la prendre par la camera", "Please_Provide_Patient_Last_Name": "SVP fournir le nom du patient", "Please_Provide_Patient_First_Name": "SVP fournir le pr�nom du patient", "Please_Provide_Patient_Age": "S`il vous pla�t fournir �ge des patients", "Please_Provide_Patient_Gender": "SVP fournir le sexe du patient", "Mobile_Number_Is_Invalid": "SVP entrer un num�ro de t�l�phone valide", "Please_Choose_image_Type_for_record": "SVP choisir une image type � enregistrer", "Patient_is_registered_successfully_patient_Id": "Patient est enregistr� avec succ�s des patients Id", "Patient_registration_is_failed": "L`enregistrement du patient a echou�", "Please_Provide_valid_DOB": "S`il vous pla�t fournir une date de naissance valide", "Invalid_Patient_First_Name": "<PERSON><PERSON><PERSON> du patient invalide", "Invalid_Patient_Last_Name": "Nom de famille du patient invalide", "Invalid_Contact_First_Name": "<PERSON><PERSON><PERSON> du <PERSON> invalide", "Invalid_Contact_Last_Name": "Nom du contact invalide", "Past_Reports": "Rapports pr�c�dents", "New_Records": "Nouveaux dossiers", "View_Consultations": "Voir les consultations", "Send_To_Doctor_for_Review": "Envoyer au m�decin pour examen", "Save_Report": "Sauvegarder le rapport", "Comment": "Commentaire", "Save_Comment": "Enregistrer un commentaire", "Report_uploaded_successfully": "Rapport t�l�charg� avec succ�s", "Report_upload_failed": "Echec du t�l�chargement du rapport", "Cant_find_slot_please_try_again": "Ne peux pas trouver le creneau svp essayer encore", "Cant_find_patient_please_try_again": "Ne peux pas trouver le patient SVP essayer encore", "Cant_find_doctor_please_try_again": "Ne peux pas trouver un m�decin SVP essayer encore", "You_are_going_to_fix_the_appointment_for_patient_name": "Vous allez fixer le rendez-vous pour le nom du patient", "Appointment_is_fixed": "<PERSON><PERSON>-vous est fix�", "This_patient_already_have_an_appointment_with_the_same_doctor_for_same_day": "Ce patient a d�j� un rendez-vous avec le  m�decin  choisi pour le  m�me jour", "Do_you_want_to_continue": "Voulez-vous continuer?", "Problem_in_fixing_appointment": "Probl�me pour fixer un rendez vous", "Appointment_Action": "Fixer un rendez-vous", "Start_Consultation": "d�but Consultation", "Or_Cancel_Appointment": "Annuler un rendez-vous", "Local_Appointment_Action": "Fixer un rendez-vous local", "Start_Local_Consultation": "Commencez consultation locale", "Problem_in_starting_consultation": "Probl�me en commen�ant la consultation", "You_cannot_start_canceled_appointment": "Vous ne pouvez pas commencer rendez-vous annul�.", "You_cannot_start_Completed_appointment": "Vous ne pouvez pas demarrer rendez-vous expir�", "Something_went_wrong_local_consultaion_Please_relogin": "<PERSON><PERSON>que chose a mal tourn� pour la consultaion svp se reconnecter", "You_cannot_cancel_active_appointment": "Vous ne pouvez annuler- rende-vous actif", "No_Doctor_Available": "Pas de docteur disponible", "Choose_Date": "S�lectionner une date", "Diognostic": "Diagnostic", "Home": "Accueil", "Suspend": "<PERSON><PERSON><PERSON><PERSON>", "Suspend_Consultation": "Suspendre la <PERSON>", "Patient_Historty": "Historique du patient", "complaints": "plaintes", "Past_Report": "Rapport pass�s", "Parameters": "Param�tres", "medication": "M�dicaments", "diagnosis": "Diagnostic", "Investigations": "Enqu�tes", "Instructions": "Conseils", "Recent_complaints_(_click_on_complaint_name_to_add_)": "R�centes plaintes (cliquez sur le nom de la plainte pour ajouter)", "History_Of_Present_Illness": "Historique de la maladie actuelle", "Past_Medical_or_Surgical_History": "Pass� m�dical / Pass� chirurgical", "Current_And_Recent_Medications": "Actuels et r�cents M�dicaments", "Other_Allergies_Or_Sensitivities": "Autres allergies ou de sensibilit�s", "Physical_Examination": "Examen physique", "Personal_History": "Histoire <PERSON>le", "Family_History": "Histoire de famille", "Medical_Allergies": "Allergies m�dicales", "Past_Consultation": "Consultation pass�es", "Past_Parameter": "Param�tre pass�", "upload_Report": "T�l�charger le rapport", "Temperature": "Temp�rature", "Spo2": "Oxym�tre", "PastRecord_ManualEntry_Msg": "Le graphique ne peut pas �tre affich� pour la saisie manuelle", "Glucose": "Glucose", "Cholesterol": "Cholest�rol", "Hemoglobin": "H�moglobine", "Systolic": "systolique", "Diastolic": "diastolique", "Pulse": "Poul", "Spo2_Percentage": "SpO2 Pourcentage", "Spo2_Pulse": "SpO2 Poul", "View_Consultation": "Voir Consultation", "Follow_up": "Suivre", "Finish_Consultation": "Terminer consultation", "Sucessfully_uploaded": "T�l�charg� avec succ�s", "Print_Email_Consultation": "Veuillez choisir les actions requises avant la fin de la consultation", "Connect_to_doctor": "Connectez-vous � un m�decin", "Back_To_Home": "<PERSON><PERSON>�<PERSON>", "Cancel_Appointment": "Annuler rendez-vous", "Problem_while_finish_consultation": "Probl�me en terminant la consultation", "This_consultation_cant_be_restart_do_you_want_to_continue": "V<PERSON> pouvez redemarrer cette consultation par l`option entrer", "PRMS": "PRMS", "Options": "les options", "My_Calendar": "Mon calendrier", "You_have_to_find_doctor_first_to_fix_appointment": "Vous devez trouver m�decin d`abord  avant de fixer un rendez-vous", "configured_Slots": "<PERSON><PERSON><PERSON> configur�s", "Date1": "Date", "Duration": "<PERSON>r�e", "Till_Time": "jusqu`� l`heure", "Allow_General_Patients": "Autoriser aux patients g�n�raux", "Add_Slots": "Ajouter des cr�neaux", "Please_Choose_Till_Date_Greater_than_From_Date": "SVP choisir une date autre que celle pr�sente", "Please_Choose_From_Date": "SVP choisir une date", "Please_Choose_Till_Date": "SVP choisir une date", "Please_Choose_From_Time": "SVP choisir � partir de maintenant", "Please_Choose_Till_Time": "SVP choisir jusqu`au temps", "Please_Choose_Till_Time_Greater_than_From_Time": "SVP choisir au-del� du temps pr�sent", "Please_Choose_Duration": "S`il vous pla�t choisissez la dur�e", "Are_you_sure_to_delete_these_slots": "�tes-vous s�r de vouloir supprimer ces emplacements?", "There_are_some_active_appointments_Are_you_sure_to_delete": "Les rdv ont �t� fix�s dans ces cr�neaux horaires. Etes vous s�r de vouloir les supprimer?", "Slots_cannot_be_deleted": "Les cr�neaux horaires ne peuvent �tre supprim�s", "You_cannot_cancel_these_slots_nurse_is_waiting": "Vous ne pouvez pas annuler ces cr�neaux horaires au moment o� l`infirmier s`appr�te � se connecter", "You_can_start_only_active_appointments": "Vous ne pouvez d�marrer qu`avec les rdvs actifs", "Problem_in_cancelling_consultation": "Probl�me dans l`annulation de la consultation", "You_cannot_cancel_an_active_appointment": "Vous ne pouvez pas annuler un rendez- actif", "You_can_start_only_active_or_suspended_appointments": "Vous pouvez d�marrer uniquement les rendez-vous actifs ou suspendus", "Remedi": "REMEDI", "Logout": "Se d�connecter", "Change_Password": "Changer le mot de passe", "Enter_Old_Password": "<PERSON><PERSON> l`ancien mot de passe", "Enter_New_Password": "Entrez un nouveau mot de passe", "Confirm_New_Password": "Confirmer le nouveau mot de passe", "Please_Provide_Complete_Information": "S`il vous pla�t fournir des informations compl�tes", "New_Password_cannot_be_same_as_old": "Nouveau mot de passe ne peut pas �tre identique � l`ancien", "Confirm_password_is_not_matched_with_new_password": "Confirm password is not matched with new password", "Request_Coupons": "Coupon", "Coupon_count": "Entrer le nombre de coupons", "Request": "<PERSON><PERSON><PERSON>", "Click": "Cliquer ici pour avoir les coupons disponibles", "Please_Contact_Admin": "Contact de l`administrateur pour les coupons", "Change_Language": "Changer de langue", "My_Profile": "Mon Profil", "New_Password_should_be_six_character_long": "Le nouveau mot de passe devrait �tre long de 6 caract�res", "Hello_Admin": "Bon<PERSON><PERSON> Administrateur", "Profile_Management": "gestion des profils", "Configuration": "Configuration", "Report": "rapport", "Switch_Video": "commutateur vid�o", "Save_photo": "Enregistrer la photo", "Doctor": "M�decin", "referrals": "R�fer<PERSON><PERSON><PERSON>", "Referral_Name": "Sp�cialisation", "complaint_name": "Nom de la plainte", "Drug_Class": "Classe (cat�gorie) de m�dicament", "medication_Brand": "<PERSON>que de m�dicaments", "Drug_Form": "Forme medicamenteuse", "Instruction": "Instruction", "Instruction_Category": "Cat�gorie d`instruction", "Instruction_Name": "Nom de l`instruction", "ICD_Code": "code de la CIM", "Diagnosis_Name": "Nom du <PERSON>", "Category_Name": "Nom de la cat�gorie", "Category": "<PERSON>�<PERSON><PERSON>", "Investigation": "Enqu�te", "Test_Code": "code du test", "lab_Name": "Nom du laboratoire", "Location": "<PERSON><PERSON>", "Switch_to_low_bandwidth": "Passer � faible bande passante", "High_Bandwidth_video_mode_is_on": "La vid�oconf�rence haut d�bit a �t� activ�e", "Low_Bandwidth_video_mode_is_on": "La vid�oconf�rence � faible bande passante a �t� activ�e", "Switch_to_High_Bandwidth": "Passer � large bande passante", "Problem_in_switching_video_mode_Please_try_again": "Probl�me dans la commutation en mode vid�o. S`il vous pla�t essayer � nouveau", "Something_went_wrong_Please_try_again": "Une erreur s`est produite. Veuillez r�essayer", "Select": "S�lectionner", "User_Name": "Nom d`utilisateur", "User_Roles": "R�les de l`utilisateur", "Domains": "Domaines", "Resources": "Ressources", "Role_Mapping": "r�le de la cartographie", "Select_Domain": "S�lectionnez domaine", "Resource_Mapping": "La cartographie des ressources", "Select_Role": "S�lectionner un r�le", "Edit_Profile": "Modifier le profil", "Super_Admin": "Super Admin", "Parameter_Mapping": "Param�trer la cartographie", "User_LoggedOut": "Deconnecter l`utilisateur", "Doctor_Domain_Configuration": "Configuration du domaine du m�decin", "adminLogin": "ADMINLOGIN", "Profile Management": "gestion des profils", "Profile": "Profil", "Honorific": "Honorifique", "Name": "<PERSON><PERSON>�<PERSON>", "State": "Etat", "District": "District", "Block": "Bloc", "Village": "Village", "Remedi_Parameter": "<PERSON><PERSON><PERSON>", "Other_Parameter": "<PERSON><PERSON> para<PERSON>", "Complaints_Data": "R�clamations", "ID": "ID", "status": "Statut", "ok": "D'accord", "Domain": "Sp�cialit�", "Resource": "Ressource", "Role": "<PERSON>�<PERSON>", "Restart_Video": "Red�marrer la vid�o", "Doctor_Information": "Informations m�decin", "Nurse_Information": "Informations infirmi�re", "Patient_Information": "Informations Patient", "Patient_history": "Historique du patient", "Please_Provide_Username": "<PERSON><PERSON><PERSON> de compl�ter votre nom d`utilisateur", "Please_Provide_Password": "<PERSON><PERSON><PERSON> de compl�ter votre mot de passe", "Another_User_already_logged_in_Retry": "Un%20autre%20utilisateur%20est%20d�j�%20connect�.%20Merci%20de%20r�essayer", "User_already_logged_in_Retry": "Un%20autre%20utilisateur%20semble%20�tre%20d�j�%20connect�.%20Merci%20de%20r�essayer%20plus%20tard", "Something_wrong_Please_try_again": "Erreur%20-%20Merci%20de%20r�essayer", "Domain_is_not_registered": "La%20sp�cialit�%20n`est%20pas%20enregistr�e", "Unauthorized_Access_Retry": "L`acc�s%20non%20autoris�.%20Recommencez", "invalid_Username_Retry": "Nom%20d`utilisateur%20invalide.%20Recommence�z", "Invalid_Password_Retry": "Mot%20de%20passe%20incorrect.%20Recommencez", "Problem_in_Login_Retry": "Probl�me%20de%20connection.%20Recommencez", "You_dont_have_permission_to_login_Retry": "Vous%20n`avez%20pas%20l`autorisation%20d`ouvrir%20une%20session.%20Recommencez", "Invalid_Username_or_Password_Retry": "Nom%20d`utilisateur%20ou%20mot%20de%20passe%20invalide%20.%20Recommencez", "ReMeDi_Parameters": "Param�tres ReMeDi", "Stethoscope": "St�thoscope", "StethoscopeBT": "St�thoscope Bluetooth", "ECG": "Electrocardiogramme", "Dermatoscope": "Dermatoscope", "Autoscope": "Autoscope", "Other_Parameters": "Autres param�tres", "Rapidtest": "Lecteur optique", "Glucose_Result": "<PERSON><PERSON> de sucre", "Cholesterol_Result": "Taux de cholest�rol", "Enter_3_digit_Batch_No": "Entrez votre code � 3 chiffres, si diff�rent de celui affich�", "If_want_to_update": "<PERSON><PERSON> � jour possible", "Take_Reading": "Lecture", "Hemoglobin_Result": "Taux d`h�moglobine", "Please_Select_Test": "Test", "DENGUE": "<PERSON><PERSON>", "HCG": "HCG", "HEPA_A": "H�patite A", "HEPA_B": "H�patite B", "HIV": "<PERSON><PERSON>", "MALARIA": "<PERSON><PERSON><PERSON><PERSON>", "SYPHILIS": "Syphilis", "URINE_CHECK": "V�rifier urine", "And_press": "<PERSON><PERSON> appuyez", "Show_result": "<PERSON><PERSON><PERSON><PERSON> le r�sultat", "Edit_Patient": "Modifier", "physical_Examination": "Examen clinique", "Search_Past_Consultations": "Rechercher une consultation pass�e", "RapidTest": "Test rapide", "parameteres_not_taken_for_this_patient": "Des donn�es n`ont pas �t� prises dans cette consultation", "may_be_spo2_reading_not_taken": "Erreur sur le taux de saturation en oxyg�ne", "incomplete_data": "<PERSON><PERSON> incompl�tes", "data_not_found": "Don�es introuvables", "may_be_ecg_reading_not_taken": "Erreur de lecture de l`ECG", "may_be_stetho_reading_not_taken": "Erreur de lecture du st�thoscope", "please_enter_3_digit_number": "Merci de rentrer votre code � 3 chiffres", "May_be_device_is_OFF_or_NOT_CONNECTED_please_try_again": "Veuillez v�rifier si l`appareil est bien connect�", "error": "<PERSON><PERSON><PERSON>", "Please_put_finger_into_SPO2_device_And_Retry": "Veuillez positionnez votre doigt dans l`oxym�tre et r�essayez", "Either_SPO2_probe_is_not_connected_OR_Dead_object_detected_Please_Retry": "Veuillez v�rifier si l`oxym�tre est connect� et si votre doigt est correctement positionn�", "Please_connect_SPO2_probe_And_Retry": "<PERSON><PERSON><PERSON> de connecter l`oxym�tre et r�essayez", "stop": "Arr�t", "Please_Manually_Reset_the_kit_and_click_on_OK_button": "S`il vous pla�t r�initialiser manuellement le kit et cliquez sur le bouton OK", "Unable_to_connect_ReMeDi_Kit_Please_make_sure": "Impossible de commencer les mesures. Merci de v�rifier les connections", "ReMeDi_kit_is_connected_properly": "ReMeDi est correctement connect�", "In_case_of_USB_ReMeDi_kit_please_enusre_that_it_is_switched_ON": "En cas de connectivi� USB, merci de v�rifier que l`appareil est allum�", "Jungo_driver_for_ReMeDi_is_installed_enabled": "Le logiciel ReMeDi a �t� install� et activ�", "Click_on_OK_once_the_above_mentioned_steps_are_done": "Cliquez sur Connection une fois que les �tapes ci-dessus sont effectu�es", "Parameter_Window_Unloaded_Please_Refresh_page_and_try_again": "<PERSON><PERSON><PERSON> <PERSON><PERSON>er la page et r�essayez", "Levels": "Niveaux", "Label_for_Level": "�tiquette pour le niveau", "Please_Enter_Labels_For_All_The_Levels": "<PERSON><PERSON><PERSON> de compl�ter les informations pour tous les niveaux", "Please_Select_Levels": "<PERSON><PERSON> de s�lectionner les niveaux", "Please_select_levels_in_location_hierarchy": "Veuillez s�lectionner des niveaux dans la hi�rarchie.", "Search_Consultations": "Rechercher les consultations", "Please_Relogin": "<PERSON><PERSON><PERSON> de vous reconnecter", "error1000": "Le param�tre n`a pas fonctionn�, merci de raf<PERSON><PERSON> la page", "error1001": "<PERSON><PERSON><PERSON> de connection, merci de r�essayer", "error1002": "<PERSON><PERSON><PERSON> de connecter la sonde de temp�rature", "Temp_In_Fahrenheit": "Temp�rature (Fahrenheit)", "ARC": "ACR", "ALB": "Albumine", "Creatinine": "Cr�atinine", "error1003": "Merci de v�rifier que le patient n`a pas le bras trop fin ou v�rifier si le brassard est correctement plac� sur le bras. Sinon merci de contacter le support technique", "error1004": "Merci de v�rifier que le patient n`a pas le bras trop fin ou v�rifier si le brassard est correctement plac� sur le bras. Sinon merci de contacter le support technique", "error1005": "Merci de faire une autre lecture car le patient semble hypertendu ou v�rifer si le brassard est correctement plac� sur le bras", "error1006": "Merci de faire une autre lecture. Pendant le test, le patient ne doit pas boug�. Ou v�rifier si le brassard est correctement plac� sur le bras", "error1007": "<PERSON><PERSON><PERSON> <PERSON>rer le syst�me et de prendre une autre lecture", "Pulse_Rate": "<PERSON><PERSON><PERSON><PERSON> cardia<PERSON>", "spo2_data": "Saturation en oxyg�ne", "stetho_data": "<PERSON><PERSON> st�t<PERSON>", "stetho_graph": "Graphique st�thoscope", "steho_volume_control": "Contr�le du volume du st�thoscope", "Time": "Temps", "Lead_1": "Lead 1", "Lead_2": "Lead 2", "Lead_3": "Lead 3", "AVR": "AVR", "AVL": "AVL", "AVF": "AVF", "V1": "V1", "V2": "V2", "V3": "V3", "V4": "V4", "V5": "V5", "V6": "V6", "Image_Type_Tab": "Type d`image", "Pharmacy_Tab": "Pharmacie", "Pharmacy_Name": "Nom de la pharmacie", "Pharmacy_Email": "Email de la pharmacie", "Patient_Id": "Identifiant patient", "Patient_Age": "�ge du patient", "x_axis": "Abscisse", "y-axis": "Ordonn�e", "Medication_Data": "M�dicaments", "Some_Slots_are_already_added_for_this_range_So_please_change_the_configuration": "Certains points sont d�j� disponibles pour cette �chelle de temps. Merci de modifier votre s�lection", "Slot_is_Added_Successfully": "Les points ont �t� ajout�s avec succ�s", "Problem_in_adding_slots": "Probl�me dans l`ajout de points", "Appointment_is_cancelled": "Le rendez-vous a �t� annul�", "Problem_in_resuming_local_consultation": "Probl�me dans la reprise de la consultation", "please_select_both_dates": "<PERSON><PERSON><PERSON> de s�lectionner les deux dates", "Available_Categories": "Cat�gories disponibles", "images": "Images", "Double_Click_Image_to_Magnify": "Cliquez sur l`image pour l`agrandir", "Double_Click_Document_to_Magnify": "Cliquez sur le document pour l`agrandir.", "View_Comments": "Voir les commentaires", "please_enter_comment": "<PERSON><PERSON><PERSON> d`entrer un commentaire", "Enable_selected": "Activer la s�lection", "Disable_Selected": "D�sactiver la s�lection", "Complaint_Name": "plainte Nom", "please_enter_Medication_Brand": "<PERSON>rc<PERSON> d`�crire une marque de m�dicament", "Medication_Brand_is_too_long": "La marque du m�dicament est trop longue", "Medication_Brand": "<PERSON><PERSON> du m�dic<PERSON>", "already_exist": "Existe d�j�", "error_occured_while_adding_Brand": "Une erreur est survenue lors de l`ajout de la marque", "please_enter_Drug_class": "<PERSON><PERSON><PERSON> d`entrer une classe de m�dicament", "Drug_class_is_too_long": "La classe du m�dicament est trop longue", "error_occured_while_adding_drug_class": "Une erreur est survenue lors de l`ajout de la classe du m�dicament", "please_enter_drug_form": "<PERSON><PERSON><PERSON> d`entrer une forme m�dicamenteuse", "Drug_form_is_too_long": "Forme m�dicamenteuse trop longue", "error_occured_while_adding_drug_form": "Une erreur est survenue lors de l`ajout de la forme m�dicamenteuse", "please_select_row": "<PERSON><PERSON><PERSON> de s�lectionner l`utilisateur", "please_enter_category": "<PERSON><PERSON><PERSON> d`entrer une cat�gorie", "category_is_too_long": "La cat�gorie est trop longue", "category": "<PERSON>�<PERSON><PERSON>", "error_occured_while_adding_category": "Une erreur est survenue lors de l`ajout de la cat�gorie", "Diagnosis_Id": "Identifiant du diagnostique", "Please_enter_lab_name": "<PERSON><PERSON><PERSON> d`entrer le nom du laboratoire", "Lab_name_is_too_long": "Le nom du laboratoire est trop long", "Lab_Name": "Nom du laboratoire", "error_occured_while_adding_lab_name": "Une erreur est survenue lors de l`ajout du nom du laboratoire", "Test_Type": "Type de test", "Sample_Type": "Type d`�chantillon", "sample_Quantity": "Quantit� d`�chantillon", "State_Id": "Etat Id", "please_select_country": "Merci de s�lectionner le pays", "please_select_State": "<PERSON><PERSON><PERSON> de s�lectionner le d�partement", "please_select_district": "Merci de s�lectionner le district", "please_select_Block": "<PERSON><PERSON><PERSON> de s�lectionner le block", "Specialization": "Sp�cialisation", "Reg_No": "Num�ro d`enregistrement", "Qualification": "Qualification", "Organization": "Organisation", "Designation": "D�signation", "Signature": "Signature", "Consultation_Id": "Identifiant de la consultation", "Contact": "Num�ro de t�l�phone", "Con_Type": "<PERSON><PERSON>n de contacter le patient", "Diagnosis_List": "<PERSON><PERSON><PERSON>", "Medicine": "M�dicament", "Frequency": "Fr�quence", "Complaint": "Plainte", "Lab": "Laboratoire", "isMandatory": "Obligatoire", "Diagnosis_Data": "<PERSON><PERSON>", "is_Provisional": "Provisoire", "Please_select_Category": "<PERSON><PERSON><PERSON> de s�lectionner la cat�gorie", "Please_select_instruction": "<PERSON><PERSON><PERSON> de s�lectionner l`instruction", "Please_select_referral": "<PERSON><PERSON><PERSON> de s�lectionner la recommandation", "Please_select_or_enter_special_instruction": "Merci d`indiquer le nom du m�dicament", "Special_instruction_is_too_long": "L`instruction est trop longue", "Please_enter_No_of_days_(less_than_10_digit)": "<PERSON>rci d`indiquer le nombre de jours", "Please_select_or_enter_medicine_name": "<PERSON><PERSON><PERSON> de s�lection<PERSON> ou d`indiquer le nom du m�dicament", "Please_enter_medicine_name": "Merci d`indiquer le nom du m�dicament", "Medicine_name_is_too_long": "Le nom du m�dicament est trop long", "Please_select_or_enter_complaint": "<PERSON><PERSON><PERSON> de s�lection<PERSON> ou d`indiquer lde quoi souffre le patient", "Please_enter_duration_(number_only)": "<PERSON>rci d`indiquer la dur�e (nombre uniquement)", "All_fields_are_mandatory": "Tous les champs sont obligatoires", "Please_select_Lab": "<PERSON><PERSON><PERSON> de s�lectionner le nom du laboratoire", "Please_select_diagnosis": "<PERSON><PERSON><PERSON> s�lection<PERSON> le <PERSON>", "Admin_Username": "Nom de l`administrateur", "Admin_Password": "Mot de passe administrateur", "Video": "Vid�o", "Potential_Adverse_Effect": "<PERSON><PERSON><PERSON> secondaire", "Investigation_Data": "Investigation donn�es", "Instruction_Data": "Instructions", "Referral_Data": "Recommandations", "Resource_ID": "ID de ressources", "Country_Code": "Code postal", "Village_ID": "village ID", "Consultation_Report": "Rapport de consultation", "Dr": "Dr", "Parameters_Readings": "Param�tres de lectures", "BMI": "BMI", "Mandatory": "Obligatoire", "Optional": "Optionnel", "Medicines": "M�dicaments", "SrNo": "<PERSON><PERSON>�<PERSON>", "Special_Instruction": "Instruction sp�cifique", "Take_Photo": "<PERSON><PERSON><PERSON> une photo", "Currency_Number_Mapping": "Choisir une devise et le format du num�ro de t�l�phone", "Choose_Currency": "Choisir la devise", "Currency_Sybbol": "Symbole de la devise", "Phone_Number_Length": "Format du num�ro de t�l�phone", "Please_Provide_Valid_Phone_length": "Merci de fournir un format de num�ro de t�l�phone valide", "Currency_Updated": "Devise et format du num�ro de t�l�phone mis � jour", "Problem_IN_Updating_Currency": "<PERSON>b<PERSON><PERSON> de mise � jour de la devise", "error1008": "P�riph�rique non connect�", "error1009": "L`appareil n`est pas en marche", "error1010": "<PERSON><PERSON><PERSON> d`ins�rer la bandelette", "error1011": "<PERSON><PERSON><PERSON> de pipeter le sang sur la bandelette", "error1012": "<PERSON><PERSON><PERSON> d`attendre le r�sultat", "Please_Wait_preparare_past_record": "<PERSON><PERSON><PERSON> pendant la pr�paration du pr�c�dent dossier", "error1013": "L`appareil n`est peut-�tre pas allum� ou connect� correctement", "error1014": "Merci de v�rifier que le thermom�tre est connect� correctement", "error1020": "<PERSON>ch mis � jour, merci d`ins�rer la bandelette", "error1021": "Lecture de l`�lectrocardiogramme incompl�te, merci de r�essayer", "IsDeleted": "Supprim�", "Patient_is_successfully_updated": "Les donn�es du patient ont �t� mise � jour avec succ�s", "Patient_updation_is_failed": "La mise � jour des donn�es patient a �chou�", "For_Manual_Entry": "Pour saisie manuelle", "Enter_Hemoglobin_Result": "Entrer le r�sultat pour l`h�moglobine", "Save_Result": "Enregistrer le r�sultat", "Please_enter_result": "<PERSON><PERSON><PERSON> d`entrer le r�sultat du test d`h�moglobine", "Result_saved_successfully": "R�sultat enregistr� avec succ�s", "referral": "R�f�rence", "fetal_dopler": "Doppler f\\u0153tal", "spiro": "Spirom�tre", "Free_Text": "Texte libre", "Yes": "O<PERSON>", "No": "Non", "Add_Medication_Brand": "Ajouter une marque de m�dicament", "Add_Drug_Form": "Ajouter une forme m�dicamenteuse", "Add_Drug_Class": "Ajouter une classe de m�dicaments", "Are_You_Sure": "En �tes-vous certain ?", "pressure": "Pression", "Add_Category": "Ajouter une cat�gorie", "Add_Lab_Name": "Ajouter un nom de laboratoire", "Please_Wait": "S`il vous pla�t, attendez", "Data_Saved": "<PERSON><PERSON> sa<PERSON>", "Error_while_saving": "E<PERSON>ur lors de l`enregistrement", "this_medicine_already_prescribed": "Ce m�dicament a d�j� �t� prescrit", "problem_occured_while_inserting_medicine": "Un probl�me est survenu lors de l`ajout du m�dicament", "problem_occured_while_updating_medicine": "Un probl�me est survenu lors de la mise � jour du nom du m�dicament", "problem_occured_while_deleteing_medicine": "Un probl�me est survenu lors de la suppression du nom du m�dicament", "Celsius": "<PERSON><PERSON><PERSON>", "Height": "<PERSON><PERSON>", "Glu": "Glucose", "chol": "Chol�st�rol", "hemo": "H�moglobine", "Temp": "Temp�rature", "BP_sys": "BP Sys", "dis": "BP Dis", "Lipid": "<PERSON><PERSON><PERSON>", "Fields_are_Mandatory": "Les champs sont obligatoires", "Upload_Image": "T�l�charger image", "Consultation_Reports": "Rapports de consultation", "Medical_Images": "Images m�dicales", "Vitals": "Vitals Et Rapports", "Generate_Reports": "G�n�rer des rapports", "Export_Reports_Between": "Rapports d`exportation entre", "separated": "S�par�(e)", "widow": "<PERSON><PERSON><PERSON>(ve)", "Please_select_or_enter_instruction": "<PERSON><PERSON><PERSON> de s�lection<PERSON> ou de saisir une recommandation", "Please_enter_instruction": "<PERSON><PERSON><PERSON> d`entrer une recommandation", "instruction_is_too_long": "La recommandation est trop longue", "Please_select_or_enter_test": "<PERSON><PERSON><PERSON> de s�lectionner ou de saisir un essai", "Please_enter_test": "<PERSON><PERSON><PERSON> de saisir un essai", "test_name_is_too_long": "Le nom du test est trop long", "Please_select_or_enter_diagnosis": "<PERSON><PERSON><PERSON> de s�lection<PERSON> ou saisir le <PERSON>", "Please_enter_both_icdcode_and_diagnosis": "Merci de saisir le code ICD et le diagnostic", "Both_AutoSelected_Diagnosis_FreeText_cannot_choose": "Le diagnostic s�lectionn� automatiquement et le texte libre ne peuvent pas �tre choisis.", "diagnosis_is_too_long": "Le diagnostic est trop long", "Total_Time_of_Usage": "Temps total d` utilisation", "Doctor_Wise": "Doc<PERSON>ur ?", "Nurse_Wise": "Infirmi�re ?", "Doctor_wise_Time_Report": "Rapport docteur", "Nurse_wise_Time_Report": "Rapport infirmi�re", "Total_Time": "Temps total", "No_Data_Found": "<PERSON><PERSON>ne donn�e disponible", "error1022": "L`appareil est en marche", "Others": "Autres", "All_Green_Color_Fields_are_Mandatory": "Tous les champs en vert sont obligatoires", "You_cannot_cancel_completed_appointment": "Vous ne pouvez pas annuler le rendez-vous pris", "This_appointment_is_already_cancelled": "Ce rendez-vous est d�j� annu�l", "problem_occurred_while_canceling_consultation": "Un probl�me est apparu lors de l`annulation de la consultation", "Generated_by": "G�n�r� par", "This_is_an_electronically_generated_report": "Ceci est un rapport g�n�r� automatiquement", "SpO2": "SpO2", "BP": "BP", "Chol": "Chol", "Hemo": "<PERSON><PERSON>", "Consultation_is_still_happening_Please_finish_that_first": "La consultation a toujours cours. <PERSON><PERSON><PERSON> de la terminer en premier lieu", "Your_Domain_has_been_Expired_Please_Renew": "Vous%20ne%20pouvez%20pas%20acc�der%20au%20serveur.%20Veuillez%20contacter%20votre%20administrateur%20pour%20r�activer%20l'acc�s.", "Your_License_has_been_Expired_Please_Renew": "Votre%20licence%20a%20expir�.%20Veuillez%20contacter%20votre%20administrateur%20pour%20renouveler%20la%20licence.", "Your_Domain_has_been_Expired_Please_Renew_Android": "Vous ne pouvez pas acc�der au serveur. Veuillez contacter votre administrateur pour r�activer l'acc�s.", "Your_License_has_been_Expired_Please_Renew_Android": "Votre licence a expir�. Veuillez contacter votre administrateur pour renouveler la licence.", "remedi_server_not_running": "Un%20composant%20ReMeDi%202.0%20ne%20fonctionne%20pas.%20Merci%20de%20red�marrer%20et%20de%20rafra�chir%20la%20page", "Please_put_finger_into_Spo2_sensor": "<PERSON><PERSON><PERSON> de placer le doigt dans le capteur", "Dead_object_detected": "Objet mort d�tect�.", "Sensor_switched_off_Please_take_reading_again": "Le capteur est �teint! Veuillez lire � nouveau.", "Problem_occured_while_updating_domain_details": "Un probl�me est survenu lors de la mise � jour des d�tails du domaine", "No_record_found": "Aucun Enregistrement Trouv�", "Error_while_logging_out": "E<PERSON>ur lors de la d�connexion", "Total_Record_Count": "Nombre total d'enregistrements", "Ignore": "<PERSON><PERSON><PERSON>", "Data_not_found": "Don�es introuvables", "Value": "<PERSON><PERSON>", "Patient_already_registered": "Patient d�j� inscrit", "Slots_not_available": "Cr�neau non trouv�", "The_patient_already_has_appointment_with_the_selected_doctor_for_the_same_day": "Le patient a d�j� rendez-vous avec le m�decin s�lectionn� pour le m�me jour", "Complete": "<PERSON><PERSON><PERSON>�", "In_Review": "En revue", "Done": "<PERSON><PERSON><PERSON>�", "Slot_time_duration_is_overlapping_with_another_appointment_Please_try_different_slot": "La dur�e des cr�neaux horaires chevauche un autre rendez-vous. <PERSON><PERSON><PERSON>z essayer un autre emplacement", "Appointment_updated_successfully": "<PERSON><PERSON>-vous mis � jour avec succ�s", "Appointment_does_not_exist_Please_create_new_appointment_first": "Le rendez-vous n'existe pas. Veuillez d'abord cr�er un nouveau rendez-vous", "Appointment_can_be_rescheduled_15_minutes_prior_to_scheduled_time": "Le rendez-vous peut �tre reprogramm� 15 minutes avant l'heure pr�vue.", "#Inqueue": null, "Connected": "Connect�", "Fail": "�chec", "Please_provide_consultation_ID": "Veuillez fournir un identifiant de consultation", "Incorrect_domain": "Domaine incorrect", "Please_provide_created_date": "Veuillez indiquer la date de cr�ation", "Appointment_is_cancelled_It_will_take_6-7_working_days_to_refund": "Le rendez-vous est annul�. Il faudra 6-7 jours ouvrables pour rembourser", "Error_while_inserting_records_in_Database": "Erreur lors de l'enregistrement des donn�es dans la base de donn�es", "REFUND_FAILED": "REFUND FAILED", "Invalid_domain": "Domaine non valide", "Error_while_processing_refund_Please_connect_to_hospital's_admin": "Erreur lors du traitement du remboursement. Veuillez vous connecter � l'administrateur de l'h�pital", "Error_while_processing_refund": "Erreur lors du traitement du remboursement", "Exception_while_refunding_payment_transaction": "Exception lors du remboursement de l'op�ration de paiement", "Appointment_can_be_cancelled_15_minutes_prior_to_scheduled_time": "Le rendez-vous peut �tre annul� 15 minutes avant l'heure pr�vue.", "Screening": "D�pistage", "#is_FollowUp_Val": null, "Cannot_make_connection": "Impossible d'�tablir la connexion", "Cannot_check_meeting": "Impossible de v�rifier la r�union", "Cannot_end_meeting": "Impossible de mettre fin � la r�union", "Problem_occured_while_updating_nurse_profile": "Un probl�me est survenu lors de la mise � jour du profil de l'infirmi�re", "Device_ID_is_NULL": "L'ID de l'appareil est NULL", "Error_while_accessing_Client_requests_OAuth_Login_URL's": "Erreur d'acc�s du client � la demande des URL de connexion", "Authentication_failed": "Echec authentification", "Please_try_after_some_time": "V<PERSON>illez essayer apr�s quelques instants", "Invalid_credentials": "Informations d'identification invalides", "Inserted_successfully": "Ins�r� avec succ�s", "Failure_to_insert": "D�faut d'insertion", "Exception_while_processing_Stethoscope_data": "Exception lors du traitement des donn�es du st�thoscope", "Exception_in_recording_login_time": "Exception dans le temps de connexion d'enregistrement", "User_seems_to_be_already_logged_in_Please_try_again_after_some_time": "Un autre utilisateur semble �tre d�j� connect�. <PERSON><PERSON><PERSON> r�essa<PERSON> plus tard", "Incorrect_username_or_password": "Identifiant ou mot de passe incorrect", "Invalid_Domain": "Domaine non valide", "Successfully_logged_out": "D�connexion r�ussie", "Password_changed_successfully": "Le mot de passe a �t� chang� avec succ�s.", "Error_while_changing_password": "Erreur lors du changement de mot de passe", "Error_while_getting_URL_from_S3": "Erreur lors de l'obtention de l'URL depuis S3", "Invalid_domain_name": "Nom de domaine invalide", "Parameter_not_enabled_for_this_domain": "Param�tre non activ� pour ce domaine", "Please_provide_valid_consultationId_Or_username": "Veuillez fournir un identifiant de consultation ou un nom d'utilisateur valide", "No_patient_exists_with_given_mobile_number": "Aucun patient n'existe avec un num�ro de portable donn�", "Please_provide_password_to_login": "Veuillez fournir un mot de passe pour vous connecter", "Error": "<PERSON><PERSON><PERSON>", "New_password_cannot_be_same_as_old_password": "Nouveau mot de passe ne peut pas �tre identique � l'ancien", "Please_provide_all_parameters": "Veuillez fournir tous les param�tres", "Exception_while_inserting_payment_status": "Exception lors de l'insertion de l'�tat du paiement", "Payment_Gateway_status_inserted_succesfully": "L'�tat du paiement a bien �t� enregistr�", "Error_occurred_to_insert_data_in_Payment_Gateway_status": "Une erreur s'est produite lors de la saisie des donn�es de paiement", "Error_occurred_to_get_Payment_Gateway_details": "Une erreur s'est produite pour obtenir les d�tails de paiement", "Exception_while_creating_payment_order": "Exception lors de la cr�ation de l'ordre de paiement", "Please_provide_payment_status": "Veuillez indiquer l'�tat du paiement", "Booked": "R�serv�", "Error_while_updating_payment_status_in_database": "Erreur lors de la mise � jour de l'�tat du paiement dans la base de donn�es", "Error_while_cancelling_consultation_ID": "Erreur lors de l'annulation de l'ID de consultation", "Nurse_profile": "Profil d'infirmier( e )", "Error_while_updating_Corona_Symptoms_Details": "Erreur lors de la mise � jour des d�tails des sympt�mes Corona virus", "Error_while_updating_Corona_Travel_History": "Erreur lors de la mise � jour de l'historique de voyage Corona virus", "Error_while_updating_Corona_Test_Details": "Erreur lors de la mise � jour des d�tails du test Corona virus", "Error_while_updating_Corona_Medical_History": "Erreur lors de la mise � jour de l'historique m�dical Corona virus", "Invalid_action": "Action non valide", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Resting_Metabolism": "M�tabolisme au repos", "Body_Weight": "Poids", "Slots_have_been_updated_successfully": "Les emplacements ont �t� mis � jour avec succ�s", "BodyComposition_ManualEntry": "<PERSON><PERSON> man<PERSON> de la composition corporelle", "Edit": "�diter", "Body_Composition": "La composition corporelle", "Edit_the_slot": "Modifier le slot", "Body_Height": "Hauteur du corps", "Skeletal_Muscle": "Muscle squelettique", "Haematology_Analyzer": "Analyseur d'h�matologie", "Successfully_saved_BodyComposition_data": "Donn�es de composition corporelle enregistr�es avec succ�s", "Lipid_Profile_POC": "Profil lipidique POC", "Glucose_POC": "Glucose POC", "Body_Age": "�ge corporel", "Visceral_Fat": "Graisse visc�rale", "Slot_is_Updated_Successfully": "L'emplacement est mis � jour avec succ�s", "Appointment_rescheduled": "Rendez-vous report�", "CCA_Test": "Test CCA", "Body_Fat": "<PERSON><PERSON><PERSON>", "HEPA_SCAN_HBsAg": "TEST HEPATITE B", "HEPA_SCAN_HCV": "TEST HEPATITE C", "TROPONIN_I": "TROPONINE", "MALERIA_P": {"f_P": {"v": "PALUDISME P.f P.v"}, "f_PAN": "PALUDISME P.f PAN", "f_Antigen": "PALUDISME P.f Antigen", "f": "PALUDISME P.f"}, "DENGUE_NS1": "DENGUE NS1", "DENGUE_IgG_IgM": "DENGUE IgG IgM", "DENGUE_IgG_IgM(Bhat_Biotech)": "DENGUE IgG IgM (BHAT BIO-TECH)", "DENGUE_IgG_IgM(SD_Standard)": "DENGUE IgG IgM (SD STANDARD)", "PREGNANCY_HCG": "TEST GROSSESSE", "HIV_TRILINE": "VIH SIDA", "HIV_I": "VIH I", "HIV_II": "VIH II", "Maleria_P": {"f": "Paludisme P.f", "v": "Paludisme P.v"}, "MALERIA_PAN": "PALUDISME PAN", "Do_you_want_to_reschedule_the_appointment": "Voulez-vous reporter le rendez-vous", "Appointment_can_be_rescheduled_prior_15_minutes_to_scheduled_time": "Le rendez-vous peut �tre report� avant 15 minutes � l'heure pr�vue", "Appointment_can_be_cancelled_prior_15_minutes_to_scheduled_time": "Le rendez-vous peut �tre annul� avant 15 minutes avant l'heure pr�vue.", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "Transgenre", "NURSE_PATIENT_MAPPING": "Cartographie des patients infirmiers", "Select_Patient": "S�lectionnez un patient", "Assign": "Attribuer", "MultiCareProfile": "Profil SD BioSensor", "View_Foot_Report": "voir le rapport de pieds", "Breast_Cancer_Screening": "D�pistage du cancer du sein", "Foot_analyzer": "Analy<PERSON>ur de pied", "F200": "F200 analyzer", "Total_CRP": "CRP", "HbA1C": "HbA1C", "U-ALB": "U-ALB", "Value_of_HbA1C": "Valeur de l'HbA1C", "Total_U-ALB": "Total UALB", "Enter_Valid_RCP_reading": "Entrez RCP_reading valide", "Enter_Valid_HBA1C_reading": "Entrez une lecture HBA1C valide", "Enter_Valid_UALB_reading": "Entrez une lecture RCP valide", "GlucoseRandom": "Glucose al�atoire", "GlucoseFasting": "Glucose je�ne", "GlucosePostprandial": "Glucose postprandiale", "Successfully_saved_Mutlicare_data": "Donn�es SD BioSensor enregistr�es avec succ�s", "Please_upload_pdf_report": "Veuillez t�l�charger le rapport PDF", "homme": "homme", "Doctors": "Doctors", "AGENT": "AGENT", "Add_Login_Logo_Name": "Ajouter le logo de l'�cran de connexion", "Please_SwitchOn_NeuroTouch_Device": "Veuillez allumer l'appareil NeuroTouch", "Yostra_Data_Sent_Success_Message": "Les donn�es ont �t� envoy�es avec succ�s", "View_Cancer_Report": "A<PERSON><PERSON><PERSON> le rapport sur le cancer", "PROJECT_NAME": "nom du projet", "PROJECT_LOCATION": "Emplacement du projet", "project_location_title": "Cartographie du centre de projet", "subscription": "Configuration de l'abonnement", "subscription_config": "Ajouter un plan d'abonnement pour la famille", "familycardid": "Identifiant unique", "row_familycardid": "Identifiant de la carte familiale", "Select_Subscription": "S�lectionnez un abonnement", "Please_Provide_Valid_FamilyCardId": "Veuillez fournir un identifiant unique valide", "subcriptionId": "Identifiant d'abonnement", "subcription_name": "Nom de l'abonnement", "no_active_subcriptione": "Aucun abonnement actif", "completed_free_subcriptione": "Abonnement gratuit termin�", "remainig_subcription": "Abonnement gratuit restant", "active_subcription": "Abonnement actif", "startdate": "Date de d�but", "enddate": "Date de fin", "subcription_free": "Abonnement gratuit", "subcription_activate": "Activer", "family_not_registered": "La famille n'est pas enregistr�e. V�rifiez s'il vous pla�t.", "subcription_expired": "Expir�", "please_select_subscription_plan": "Veuillez s�lectionner le plan d'abonnement", "plan_already_expired": "Le forfait a d�j� expir�", "subscription_expire_on": "L'abonnement expire le", "subscription_activated_on": "Abonnement activ� le", "subscription_activate_from": "Le forfait a d�j� expir�", "startdate_enddate_check": "La date de fin doit �tre sup�rieure ou �gale � la date de d�but.", "subscription_already_activation_notification": "Le plan d'abonnement s�lectionn� est actuellement actif pour le groupe Famille. �tes-vous s�r de vouloir le d�sactiver ?", "click_here_to_subscribe": "Cliquez ici pour vous abonner", "click_here_to_reactivate_subscribe": "Cliquez ici pour r�activer l'abonnement", "project_location_heading": "Emplacement", "project_center": "Centre", "project_center_status": "Statut du centre", "please_select_project_location": "Veuillez s�lectionner l'emplacement", "please_select_center": "Veuillez s�lectionner le centre", "select_location": "S�lectionnez l'emplacement", "project_center_id": "centerId", "Error_message_center_disabled": "Veuillez%20contacter%20administrateur", "niramaiUserName": "Niramai nom d'utilisateur", "niramaiPassword": "Mot de passe Niramai", "Appointment_is_Removed": "Le rendez-vous a �t� cl�tur�", "Problem_in_Closing_Appt": "Probl�me de fermeture de rendez-vous", "You_cannot_start_done_appointment": "Vous ne pouvez pas commencer le rendez-vous termin�", "Select_Dose": "S�lectionnez la dose", "Select_Frequency": "S�lectionnez la fr�quence", "param_BloodPressure": "Pression art�rielle", "param_Blood_Pressure": "Pression art�rielle", "param_Electrocardiogram": "�lectrocardiogramme", "param_PulseOximeter": "Oxym�tre de pouls", "param_Pulse_Oximeter": "Oxym�tre de pouls", "param_SpiroMeter": "Spirom�tre", "param_Spiro_Meter": "Spirom�tre", "param_StethoScope": "St�thoscope", "param_Stetho_Scope": "St�thoscope", "param_Temperature": "Temp�rature", "param_Glucose": "Random Blood Sugar", "param_Fasting_Blood_Sugar": "Fasting Blood Sugar", "param_Post-prandial_Blood_Sugar": "Post-prandial Blood Sugar", "param_Hemoglobin": "H�moglobine", "param_Lipid": "<PERSON><PERSON><PERSON>", "param_Rapidtest": "Test rapide", "param_Urinetest": "Test d'urine", "param_FetalDopler": "Doppler f\\u0153tal", "param_Hba1c": "HBA1C", "param_HBA1C": "HBA1C", "param_ARC": "ARC", "param_ACR": "ACR", "param_PatientMonitor": "<PERSON><PERSON><PERSON>", "param_Dermatoscope": "Dermatoscope", "param_Otoscope": "Otoscope", "param_Ultrasound": "Ultrason", "param_CCA_Test": "Test ACC", "param_Haematology_Analyzer": "Analyseur d'h�matologie", "param_ClinicalChemistryTests": "CliniqueChimieTests", "param_Clinical_Chemistry_Tests": "Tests de chimie clinique", "param_HaematologyTests": "H�matologieTests", "param_Haematology_Tests": "Tests d'h�matologie", "param_BodyComposition": "La composition corporelle", "param_Body_Composition": "La composition corporelle", "param_F200Analyzer": "Analyseur F200", "param_FootAnalysisReport": "Rapport d'analyse de pied", "param_BreastCancerScreening": "D�pistage du cancer du sein", "param_Breast_Cancer_Screening": "D�pistage du cancer du sein", "param_CRP": "PCR", "param_microalbumin": "microalbumine", "param_Respiratory": "Respiratoire", "Installer": "Installateur", "Disable": "D�sactiver", "Enable": "Permettre", "Monthly_Report": "Rapport mensuel", "Select_Year": "S�lectionnez l'ann�e", "Select_Month": "S�lectionnez un mois", "January": "janvier", "February": "f�vrier", "March": "mars", "April": "avril", "May": "<PERSON>", "June": "juin", "July": "juillet", "August": "ao�t", "September": "septembre", "October": "octobre", "November": "novembre", "December": "d�cembre", "Generate_Stock": "G�n�rer des stocks", "Cancer": "Cancer", "Patient": "Patient", "Name/Username": "Nom/Nom d'utilisateur", "Please_select_Year_and_Month": "Veuillez s�lectionner l'ann�e et le mois", "Choose_File": "<PERSON><PERSON> le <PERSON>", "Export_to_excel": "Exporter vers excel", "Dashboard": "MIS Tableau de bord", "Total_Consultation": "Consultation totale", "DoctorLocal": "m�decin", "DoctorRemote": "m�decin", "NurseLocal": "infirmier / patient", "NurseRemote": "infirmier / patient", "Local": "Locale", "Remote": "� distance", "Patient_ID": "Carte d'identit� du patient", "Registrations": "Inscriptions", "Top_diagnosis": "Top diagnostic", "Collapse": "<PERSON><PERSON>e<PERSON><PERSON><PERSON>", "Remove": "<PERSON><PERSON><PERSON>", "top_investigations": "meilleures enqu�tes", "Loading": "Chargement", "Patient_Profile": "<PERSON><PERSON> du <PERSON>", "Sensor_Usage": "Utilisation du capteur", "Top_ten_medicine": "Top 10 des m�dicaments", "Log": "Enregistrer", "Out": "<PERSON> de<PERSON>s", "Are_you_sure_you_want_to_log_out?": "�tes-vous s�r de vouloir vous d�connecter?", "Press_No_if_you_want_to_continue_work": {"_Press_Yes_to_logout_current_user": {"": "Appuyez sur Non si vous souhaitez continuer � travailler. Appuyez sur Oui pour d�connecter l'utilisateur actuel."}}, "MIS_Report": "Rapport SIG", "Detail_Consultation_Report": "Rapport de consultation d�taill�", "Consultation_Summary": "R�sum� de la consultation", "Export_Data": "Exporter des donn�es", "Age_Filter": "Filtre d'�ge", "Duration_filter": "Filtre de dur�e", "Select_Age_Range": "S�lectionnez la tranche d'�ge", "Select_Duration_range": "S�lectionnez la tranche dur�e", "Save_changes": "Sauvegarder les modifications", "Visit": "Visiter", "Sex": "<PERSON>e", "Date": "Date", "Speciality": "Sp�cialit�", "Aayushman_Bharat": "<PERSON><PERSON><PERSON><PERSON>", "Aayushman_Bharat_Survey": "<PERSON><PERSON><PERSON>", "Aayushman_Bharat_Survey_Details": "D�tails de l'enqu�te A<PERSON><PERSON>", "Name_OF_Patient": "Nom DU Patient", "ID_No": "ID_No", "Date_of_Examination": "Date d'examen", "Is_the_Patient_eligible_for_Aayushman_Bharat_Golden_Card?": "Le patient est-il �ligible � la carte dor�e Aayushman Bharat\\u00A0?", "Do_you_have_a_Aayushman_Bharat_Golden_Card?": "<PERSON><PERSON>-vous une carte dor�e Aay<PERSON>man Bharat\\u00A0?", "Have_you_ever_used_Aayushman_Bharat_Golden_Card?": "<PERSON><PERSON>-vous d�j� utilis� <PERSON> B<PERSON>t Golden Card ?", "Billing": "Facturation", "Doctor_Username": "Nom d'utilisateur du m�decin", "Total_Test_Wise_Fees": "Total des frais de test Wise", "Line_Chart": "Graphique en ligne", "Total_Fees": "Total des frais", "Consultation_Fees": "Frais de consultation", "Diagnosis_Fees": "Frais de diagnostic", "Bill_Details": "D�tails de la facture", "Foot_Analysis_Report": "Rapport d'analyse de pied", "Microalbumin": "microalbumine", "CRP": "PCR", "Labtest": "Test de laboratoire", "ACR_Fees": "Frais ACR", "HbA1c_Fees": "Frais HbA1c", "Fetal_Fees": "Frais f\\u0153taux", "UrineTest_Fees": "Frais de test d'urine", "Rapid_Fees": "Frais rapides", "Lipid_Fees": "<PERSON><PERSON> de lipides", "Hemoglobine_Fees": "Frais d'h�moglobine", "Glucose_Fees": "Frais de glucose", "Temperature_Fees": "Frais de temp�rature", "Stethoscope_Fees": "Frais de st�thoscope", "Spiro_Fees": "<PERSON><PERSON>", "Spo2_Fees": "Frais Spo2", "ECG_Fees": "Frais ECG", "BloodPressure_Fees": "Frais de tension art�rielle", "Total_Diagnostics_Fees": "Total des frais de diagnostic", "Prescription_Fees": "Frais d'ordonnance", "Center_Wise_Investigation": "Centre d'enqu�te sage", "Top_Centers": "Meilleurs centres", "TOP_10": "TOP 10", "Center": "Centre", "Form": "Forme", "No_of_Prescription": "Nbre de prescription", "Top_Medicines": "Meilleurs m�dicaments", "Doctor_Wise_Reports": "Rapports du sage docteur", "Doctor_Wise_Consultation_Report": "Rapport de consultation de Doctor <PERSON>", "General_Consultation": "Consultation g�n�rale", "Specialized_Consultation": "Consultation sp�cialis�e", "Incomplete": "Incompl�te", "Patient_Wise_Details": "D�tails sages du patient", "ACR(CRE)": "ACR(CRE)", "ACR(ALB)": "ACR(ALB)", "ACR(Acr)": "ACR(RTA)", "HbA1c(%)": "HbA1c(%)", "HbA1c(eAG)": "HbA1c(eAG)", "HbA1c(mmol)": "HbA1c(mmol)", "Spiro(TLC)": "Spiro(TLC)", "Spiro(TV)": "<PERSON><PERSON><PERSON> (TV)", "Spiro(PEF)": "Spiro(PEF)", "Spiro(FVC)": "Spiro(CVF)", "Spiro(FEV1)": "Spiro(FEV1)", "Spiro(FEV1%)": "Spiro (FEV1%)", "Spiro(FEF25)": "<PERSON><PERSON><PERSON> (FEF25)", "Spiro(FEF50)": "S<PERSON>ro (FEF50)", "Spiro(FEF75)": "<PERSON><PERSON><PERSON> (FEF75)", "Urine(GLU)": "<PERSON><PERSON>(GLU)", "Urine(LEU)": "<PERSON><PERSON> (UFE)", "Urine(NIT)": "Urine (NIT)", "Urine(URO)": "<PERSON><PERSON>(URO)", "Urine(PRO)": "<PERSON><PERSON>(PRO)", "Urine(pH)": "Urine(pH)", "Urine(BLO)": "<PERSON><PERSON>(BLO)", "Urine(SG)": "<PERSON><PERSON>(SG)", "Urine(KET)": "<PERSON><PERSON>(KET)", "Urine(BIL)": "<PERSON><PERSON>(BIL)", "Lipid(TC)": "<PERSON><PERSON><PERSON> (TC)", "Lipid(TG)": "Li<PERSON><PERSON> (TG)", "Lipid(HDL)": "<PERSON><PERSON><PERSON> (HDL)", "Lipid(LDL)": "<PERSON><PERSON><PERSON> (LDL)", "BP(Pulse)": "PA (impulsion)", "BP(Dia)": "BP(Dia)", "BP(Sys)": "PA(Sys)", "Sys": "Syst�me", "Dia": "<PERSON>a", "Fetal_Count": "Nombre f\\u0153tal", "Stetho_Count": "<PERSON><PERSON><PERSON> st�tho", "Ecg_Count": "Nombre d'ecg", "Problem_for_which_Examined": "Probl�me pour lequel examin�", "Chief_Complaints": "<PERSON><PERSON> plaintes", "Patient_Status": "Statut du patient", "Project_Id": "Identifiant du projet", "Breast_Cancer_Report_Taken": "Rapport de cancer du sein pris", "Foot_Analysis_Report_Taken": "Rapport d'analyse de pied pris", "Nurse_Wise_Reports": "Rapports d'infirmi�re avis�e", "Nurse_Wise_Consultation_Report": "Rapport de consultation Infirmi�re avis�e", "Location_Wise_Consultation_Report": "Rapport de consultation de Location Wise", "Location_Wise_Reports": "Rapport d'emplacement", "Center_Wise_Report": "rapport sage du centre", "Nurse_Wise_Medicine": "Infirmi�re sage m�decine", "Center_Wise_Medicine": "Centre de m�decine sage", "Consumable": "Consommable", "Consumables_Store": "<PERSON><PERSON><PERSON> de consommables", "Select_quantity_provide_email_and_place_order": "S�lectionnez la quantit�, fournissez un e-mail et passez la commande", "Opps_Consumables_are_not_available": {"": "Les consommables Opps ne sont pas disponibles."}, "Enter_your_email_id_and_place_order": "Entrez votre identifiant e-mail et passez la commande", "Place_Order": "Passer la commande", "top_diagnosis": "meilleur diagnostic", "XLS": "XLS", "PDF": "PDF", "CSV": "CSV", "0-20_Years": "0-20 ans", "21-40_Years": "21-40 ans", "41-60_Years": "41-60 ans", "61-100_Years": "61-100 ans", "Doctor_Wise_Diagnosis": "Diagnostic du docteur Wise", "Nurse_Wise_Diagnosis": "Nurse Wise Diagnosis", "Enter_Quantity": "Entrez la quantit�", "Email_Address": "Adresse e-mail", "In_Queue": "Dans la queue", "Doctor_is_waiting": "Le docteur attend", "Cancelled_by_doctor": "Annul� par le m�decin", "Cancelled_by_you": "Cancelled by you", "Patient_screening_in_progress": "Annul� par vous", "Patient_is_waiting": "Le patient attend", "Cancelled_by_nurse": "Annul� par l'infirmi�re", "Edit_by_nurse": "Modifier par l'infirmi�re", "Center_Wise_Counselling": "Centre de conseils avis�s", "Doctor_Wise_Counselling": "Doctor Wise Counseling", "Nurse_Wise_Counselling": "Conseil infirmier sage", "Counselling": "Conseils", "Doctor_Wise_Counselling_Dashboard": "Tableau de bord de conseil Doctor Wise", "Nurse_Wise_Counselling_Dashboard": "Tableau de bord de counseling Nurse Wise", "ALL": "TOUT", "Counselling_Dashboard": "Tableau de bord du conseil", "Top_Counselling": "Haut Conseil", "Counselling_Name": "Nom du conseiller", "Center_Name": "Nom du centre", "Top_Doctors": "Meilleurs m�decins", "Top_Nurses": "Meilleures infirmi�res", "para_ACR": "ACR", "para_Temperature": "Temp�rature", "para_BloodPressure": "Pression art�rielle", "para_PulseOximeter": "Oxym�tre de pouls", "para_Glucose": "Glucose", "para_Rapidtest": "Test rapide", "para_Urinetest": "Test d'urine", "para_Lipid": "<PERSON><PERSON><PERSON>", "para_SpiroMeter": "Spirom�tre", "para_HBA1C": "HBA1C", "para_F200Analyzer": "Analyseur F200", "Center_Wise_Diagnosis_Dashboard": "Tableau de bord de diagnostic Center Wise", "Diagnosis_Dashboard": "Tableau de bord de <PERSON>", "Center_Wise_Diagnosis": "Diagnostic avis� du centre", "Doctor_Wise_Diagnosis_Dashboard": "Tableau de bord de diagnostic Doctor Wise", "Nurse_Wise_Diagnosis_Dashboard": "Tableau de bord de diagnostic Nurse Wise", "CenterWise_Prescription_Dashboard": "Tableau de bord de prescription Center Wise", "Prescription_Dashboard": "Tableau de bord des ordonnances", "Brand_Name": "Marque", "Doctor_Wise_Prescription_Dashboard": "Tableau de bord de prescription Doctor Wise", "Doctor_Wise_Medicine": "docteur sage m�decine", "Center_Wise_Referral_Dashboard": "Tableau de bord de r�f�rence Center Wise", "Referral_Dashboard": "Tableau de bord de r�f�rence", "top_referrals": "meilleures r�f�rences", "Doctor_Wise_Referrals_Dashboard": "Tableau de bord des r�f�rences Doctor Wise", "Referrals_Dashboard": "Tableau de bord des r�f�rences", "Doctor_Wise_Referrals": "R�f�rences Doctor Wise", "Nurse_Wise_Referrals": "R�f�rences infirmi�re sage", "Investigation_Name": "Nom de l'enqu�te", "Nurse_Wise_Investigation": "Enqu�te infirmi�re sage", "Investigation_Dashboard": "Tableau de bord d'enqu�te", "Doctor_Wise_Investigation": "Enqu�te du docteur Wise", "Doctor_Wise_Investigation_Dashboard": "Tableau de bord d'enqu�te Doctor Wise", "English": "<PERSON><PERSON><PERSON>", "Hindi": "hindi", "Spanish": "Espagnole", "French": "fran�ais", "No_records_found": "Aucun enregistrement trouv�", "SMS_Notification": "Notification par SMS", "Email_Notification": "Notification par e-mail", "Please_select_the_slots_to_be_deleted": "Veuillez s�lectionner les cr�neaux � supprimer.", "Write_Comment": "<PERSON><PERSON> un commentaire", "Doctor_Wise_Details": "Doc<PERSON>ur Wise D�tails", "Select_OP_Room": "S�lectionnez la salle d'op�ration", "Login_Time": "Heure de connexion", "Logout_Time": "Heure de d�connexion", "Spo2%": "Spo2%", "Sys_Dia_Pulse": "Sys~Dia~Impulsion", "TC_TG_HDL_LDL": "TC_TG_HDL_LDL", "HbA1c_mmol_eAG": "HbA1c(%~mmol~eAG)", "UrineTest_LEU": "Test d'urine (LEU/NIT/URO/PRO/pH/BLO/SG/KET/BIL/GLU)", "Spiro_FVC": "Spiro(CVF/FEV1/FEV1%/FEF25/FEF50/FEF75/PEF/TV/TLC)", "ACR_Acr_Alb_Cre": "ACR(Acr~Alb~Cre)", "Center_Wise_Reports": "Rapports Center Wise", "Center_Wise_Diagnostic_Report": "Rapport de diagnostic Center Wise", "Stetho": "St�tho", "Fetaldoppler": "Doppler f\\u0153tal", "Doctor_Wise_Diagnostic_Report": "Rapport de diagnostic de Doctor Wise", "Nurse_Wise_Diagnostic_Report": "Rapport de diagnostic Nurse Wise", "Glucose_Random": "Glyc�mie Al�atoire", "Glucose_Fasting": "<PERSON>e glyc�mi<PERSON>", "Glucose_Postprandial": "Glyc�mie postprandiale", "LIS": "LIS", "Summary": "<PERSON><PERSON><PERSON>", "Patient_Survey": "Sondage aupr�s des patients", "Request_for_Consumables": "<PERSON><PERSON><PERSON> de consommables", "Feedback": "Retour d'information", "Sign_Out": "Se d�connecter", "ReMeDi": "ReMeDi", "Doctor_Wise_Report": "Rapport du docteur Wise", "Nurse_Wise_Report": "Rapport Infirmi�re avis�e", "Diagnostic": "Diagnostique", "ACR": "ACR", "Referrals_Name": "Nom des filleuls", "Consultation_Feedback_Summary": "R�sum� des commentaires de la consultation", "Consultation_Feedback_Report": "Rapport de r�troaction sur les consultations", "How_did_you_come_to_know_about_the_Center?": "Comment avez-vous connu le Centre ?", "For_which_health_problem_did_you_go_to_the_Center?": "Pour quel probl�me de sant� �tes-vous all� au Centre ?", "Were_any_pathological_test_done_at_the_Center?": "Des tests pathologiques ont-ils �t� effectu�s au Centre\\u00A0?", "If_yes,_then_which_all_tests_were_done?": "Si oui, alors quels tests ont �t� effectu�s ?", "Did_doctors_from_Nanavati_Hospital_discuss_your_health_condition_over_the_web_camera?": "Les m�decins de l'h�pital Nanavati ont-ils discut� de votre �tat de sant� via la cam�ra Web\\u00A0?", "Did_the_Center_give_you_any_prescription_for_medicine?": "Le Centre vous a-t-il donn� une ordonnance pour des m�dicaments ?", "As_per_the_doctor_prescription,_Did_you_take_medicine?": "Conform�ment � l'ordonnance du m�decin, avez-vous pris des m�dicaments\\u00A0?", "Did_you_complete_course_of_medicine_as_prescribed_by_doctor?": "Ave<PERSON>-vous termin� le cours de m�decine tel que prescrit par le m�decin\\u00A0?", "Did_you_feel_better_by_this_treatment?": "Vous �tes-vous senti mieux gr�ce � ce traitement ?", "Did_you_go_to_the_Center_again?": "�tes-vous retourn� au Centre ?", "If_not,_then_why?": "Si non, alors pourquoi ?", "Would_you_prefer_being_diagnosed": "Vous pr�f�rez �tre diagnostiqu� ?", "How_much_would_you_pay_if_this_all_tests_and_treatment_were_done_by_another_doctor?": "Combien paieriez-vous si tous ces tests et traitements �taient effectu�s par un autre m�decin\\u00A0?", "Did_any_other_person_from_your_family_go_to_the_Center_for_treatment?": "Est-ce qu'une autre personne de votre famille s'est rendue au Centre pour se faire soigner\\u00A0?", "Did_you_refer_any_other_person_to_the_center_for_treatment?": "<PERSON><PERSON>-vous r�f�r� une autre personne au centre pour un traitement\\u00A0?", "What_other_facility_do_you_want_at_the_Center?": "Quelle autre installation souhaitez-vous au Centre\\u00A0?", "How_is_the_behavior_of_the_staff_working_at_the_TMC_Center?": "Comment est le comportement du personnel travaillant au Centre TMC\\u00A0?", "How_did_you_find_cleanliness_and_other_facilities_like_water,_toilets_etc_at_the_center?": "Comment avez-vous trouv� la propret� et les autres installations comme l'eau, les toilettes, etc. au centre\\u00A0?", "Consultation_Fee_Off": "r�duction", "Range": "<PERSON><PERSON><PERSON>", "Survey": "Enqu�te", "Survey_Details": "D�tails de l'enqu�te", "Waist": "<PERSON><PERSON>", "Hip": "Han<PERSON>", "If_anyone_in_your_family_(blood_relation*)_have/had_history_of_diabetes_or_hypertension_then_Enter": "Si un membre de votre famille (parent par le sang*) a/a eu des ant�c�dents de diab�te ou d'hypertension, entrez", "Medication_name": "Nom du m�dicament", "How_many_days_did_you_take_it?": "Combien de jours l'as-tu pris ?", "How_many_times_per_day_did_you_take_it?": "Combien de fois par jour l'avez-vous pris ?", "How_much_did_you_take_each_time?": "<PERSON><PERSON><PERSON> as-tu pris � chaque fois ?", "How_many_times_did_you_miss_taking_it?": "<PERSON><PERSON><PERSON> de fois avez-vous manqu� de le prendre ?", "For_what_reason_were_you_taking_it?": "Pour quelle raison le preniez-vous ?", "How_well_does_this_medicine_work_for_you?": "Dans quelle mesure ce m�dicament fonctionne-t-il pour vous\\u00A0?", "Medication_Name_bother_you_in_any_way": "Le nom du m�dicament vous d�range de quelque fa�on que ce soit", "In_what_way_does_it_bother_you?": "En quoi cela vous d�range-t-il ?", "My_medication_causes_side_effects": {"": "Mes m�dicaments causent des effets secondaires."}, "It_is_hard_to_remember_all_the_doses": "l est difficile de se souvenir de toutes les doses", "It_is_hard_to_pay_for_the_medication": "Il est difficile de payer les m�dicaments", "It_is_hard_to_open_the_container": {"": "Il est difficile d'ouvrir le r�cipient."}, "It_is_hard_to_get_my_refill_on_time": "Il est difficile d'obtenir ma recharge � temps", "It_is_hard_to_read_the_print_on_the_container": "Il est difficile de lire l'impression sur le r�cipient", "The_dosage_times_are_inconvenient": "Les temps de dosage sont peu pratiques", "My_medication_causes_other_problem_or_concern": "Mes m�dicaments causent d'autres probl�mes ou pr�occupations", "If_other_problem_or_concern,_please_explain": "Si autre probl�me ou pr�occupation, veuil<PERSON>z expliquer", "If_you_stop_taking_any_medications_in_the_PAST_SIX_MONTHS_then,_Medication_name": "Si vous arr�tez de prendre des m�dicaments au cours des SIX DERNIERS MOIS, le nom du m�dicament", "How_well_did_the_medicine_work_for_you?": "Dans quelle mesure le m�dicament a-t-il fonctionn� pour vous\\u00A0?", "How_much_did_it_bother_you?": "� quel point cela vous a-t-il d�rang�\\u00A0?", "For_what_reason_did_you_stop_taking_it?": "Pour quelle raison avez-vous arr�t� de le prendre ?", "No_of_days_consuming_fruits": "Nombre de jours de consommation de fruits", "No_of_servings_you_eat_on_typical_day": "Nombre de portions que vous mangez au cours d'une journ�e type", "No_of_days_consuming_Vegetable": "Nombre de jours de consommation de l�gumes", "Type_of_Physical_activity": "Type d'activit� physique", "Self-testing_of_the_blood_glucose": "Autocontr�le de la glyc�mieSelf-testing of the blood glucose", "Self-testing_of_the_blood_pressure": "Autotest de la tension art�rielle", "Foot_care": "Soin des pieds", "Alcohol_use_Current_user": "Consommation d'alcool Consommateur actuel", "Frequency_of_alcohol_use": "Fr�quence de consommation d'alcool", "Tobacco_use-smoke_Current_user": "Tabagisme-fum�e Utilisateur actuel", "Frequency_of_Tobacco_use": "Fr�quence de consommation de tabac", "Fear_of_side_effects_of_medicines?": "Peur des effets secondaires des m�dicaments ?", "Distressed_with_differential_food_in_the_family?": "Afflig� par la nourriture diff�rentielle dans la famille\\u00A0?", "Fear_of_complications_Of_NCD?": "Peur des complications des MNT ?", "Difficult_to_keep_up_with_the_routine?": "Difficile de suivre la routine ?", "Inability_of_the_family_members": "Incapacit� des membres de la famille", "Distressed_with_social_issues": "Afflig� par les probl�mes sociaux", "HBA1C": "HBA1C", "Center_Wise_Prescription_Dashboard": "Tableau de bord de prescription Center Wise", "top_medicines": "meilleurs m�dicaments", "Press_No_if_youwant_to_continue_work": {"_Press_Yes_to_logout_current_user": {"": "Appuyez sur Non si vous souhaitez continuer � travailler. Appuyez sur Oui pour d�connecter l'utilisateur actuel."}}, "Center_Wise_Referrals": "R�f�rences sages du centre", "Nurse_Wise_Prescription_Dashboard": "Tableau de bord de prescription Nurse Wise", "HH_Survey_Report": "HH Rapport d'enqu�te", "as_per_Friedewald_Method": "as per \"Friedewald\" Method", "as_per_Iranian_Method": "as per \"Iranian\" Method", "Patient_Contact_Details": "Coordonn�es des patients", "Pharmacy_Details": "D�tails de la pharmacie", "Fetal_doppler_pulse": "Fetal doppler pulse", "�_ALB": "�-ALB", "Foot_analysis": "Foot analysis", "Breast_cancer": "Breast cancer", "Complete_Consultation": "Complete Consultation", "Incomplete_Consultation": "Incomplete Consultation", "New_Consultation": "New Consultation", "FollowUp_Consultation": "FollowUp Consultation", "General_Consultation_Remote": "General Consultation (Remote)", "Specialized_Consultation_Remote": "Specialized Consultation (Remote)", "Pulse_oximeter": "Pulse-oximeter", "Glucometer": "Glucometer", "Spiro": "<PERSON><PERSON><PERSON>", "Rapid_Test": "Rapid Test", "L_sec": "L/sec", "from": "� partir de", "Result_is": "Le r�sultat est", "pH": "pH", "KETONE": "KETONE", "U_ALB": "&micro;ALB", "FootAnalysisReport": "Foot Analysis Report", "BreastCancerScreening": "Breast Cancer Screening", "Consent_Request_Form": "Formulaire de demande de consentement", "Patient_Identifier": "Identifiant du patient", "Purpose_Of_Request": "Objet de la demande", "Health_Info_From": "Infos sant� de", "Health_Info_To": "Infos sant� \\u2021", "Health_Info_Type": "Type d'informations sur la sant�", "Consent_Expiry": "Expiration du consentement", "Submit_Consent_Form": "Soumettre le formulaire de consentement", "CAREMGT": "Gestion des soins", "BTG": "<PERSON><PERSON> le verre", "PUBHLTH": "Sant� publique", "HPAYMT": "Paiement des soins de sant�", "DSRCH": "Recherche sur les soins de sant� sp�cifiques \\u2021 une maladie", "PATRQT": "Auto-demand�", "DiagnosticReport": "Rapport diagnostique", "OPConsultation": "Consultation du PO", "DischargeSummary": "R�sum� de d�charge", "ImmunizationRecord": "Dossier de vaccination", "HealthDocumentRecord": "Enregistrer l'artefact", "WellnessRecord": "Dossier de bien-�tre", "Add_Diagnosis": "<PERSON><PERSON><PERSON>ag<PERSON>", "Reset_Button": "R�initialiser", "Please_Enter_Diagnosis": "Veuillez Entrer <PERSON>", "Please_Select_Sub_chapter": "Veuillez s�lectionner sous-chapitre", "Please_Select_Category": "Veuillez s�lectionner cat�gorie", "Enter_Diagnosis": "Entrez le Diagnostic", "Enter_ICD_Code": "Entrer le code ICD", "Select_Sub_Chapter": "S�lectionnez le sous-chapitre", "Sunday": "<PERSON><PERSON><PERSON>", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "masculino": "<PERSON>�<PERSON>", "hembra": "<PERSON>mme", "otro": "<PERSON><PERSON>", "Fetal_Doppler_Data": "Fetal Doppler Data", "Recommondations": "Recommondations", "Intervention": "Intervention", "from_time": "De", "till_time": "A", "Summary_Parameter": "<PERSON><PERSON><PERSON>", "Summary_Medicines": "<PERSON><PERSON><PERSON>", "Summary_Diagnosis": "<PERSON><PERSON><PERSON>", "Summary_Investigation": "<PERSON><PERSON><PERSON>", "Summary_Counselling": "<PERSON><PERSON><PERSON>", "cms": "cms", "Kgs": "Kgs", "Lip_TC": "Lip TC", "Minutes": "Minutes", "Please_provide_contact_number": "Veuillez fournir le num�ro de contact.", "Add_New_Instruction": "Ajouter", "Add_New_Referral": "Ajouter", "Add_New_Investigation": "Ajouter", "Add_New_Medication": "Ajouter", "Add_New_Complaint": "Ajouter", "Add_Instruction": "Ajouter", "Add_Referral": "Ajouter", "Add_Investigation": "Ajouter", "Add_Medication": "Ajouter", "Add_Complaint": "Ajouter", "Medication": "M�dicaments", "take_live_photo": "capture d` image", "Existing_Patient": "Patient existant", "from_date": "A partir de la date", "till_date": "Jusqu`� la date", "Session": "Session", "StartTime": "<PERSON><PERSON>", "N/A": "N/A", "hour": "heure", "minute": "minute", "second": "seconde", "Contact_person_mobile_number": "Num�ro de portable de la personne � contacter.", "Please_provide_patient_details": "Veuillez fournir les d�tails du patient.", "Year": "<PERSON>", "Month": "<PERSON><PERSON>", "Day": "Jour", "and": "et", "BloodPressure": "BloodPressure", "glucose": "Glucose", "ecg": "Electro Cardio Gram", "Top_Investigation_Doctors": "Meilleurs m�decins", "Top_Counselling_Doctors": "Meilleurs m�decins", "country": "Pays", "state": "�tat", "district": "District", "block": "Bloc", "village": "Village", "city": "Ville", "Phone_Number_Min_Length": "Num�ro de t�l�phone Longueur minimale", "Phone_Number_Max_Length": "Longueur maximale du num�ro de t�l�phone", "Time_of_Examination": "Heure_de_l'examen", "Add_Medicine_Name": "Ajouter le nom du m�dicament", "error_occured_while_adding_medicine_name": "une erreur s'est produite lors de l'ajout du nom du m�dicament", "Generic_Name": "Nom g�n�rique", "No_of_Days_Cannot_Be_0_please_enter_valid_number": "Le nombre de jours ne peut pas �tre 0. Veuillez entrer un nombre valide.", "Medicine_Inventory": "Inventaire des m�dicaments", "Please_enter_No_of_days_(b/w_1_to_100)": "Veuillez saisir le nombre de jours (n/b 1 � 999)", "Please_enter_duration_(number_b/w_1_to_100)": "Veuillez entrer la dur�e (chiffre b/w 1 � 100)", "CONSULTATION_BALANCE_OVER": "Solde consultation insuffisant. Veuillez contacter votre administrateur.", "Center_Nurse_Wise_Report": "Centre Nurse Wise Report", "Medicine_Stock_Details": "D�tails du stock de m�dicaments", "Name_of_Drug": "Nom du m�dicament", "Type": "Taper", "Expired_Medicines": "M�dicaments p�rim�s", "Total_issued_medicines_from": "Total des m�dicaments d�livr�s depuis", "New_Stock_Added": "Nouveau stock ajout�", "As_per_old_count": "Selon l'ancien d�compte", "Available_Quantity": "quantit� disponible", "Quantity_To_Dispense": "QQuantit� � distribuer", "Please_enter_a_number_less_than_or_equal_to": "Veuillez entrer un nombre inf�rieur ou �gal �", "dispensed_Date": "Date de distribution", "NOT_YET_DISPENSED": "Pas encore distribu�", "DUPLICATE_BATCH_EXPIRY_ENTRY": "Num�ro de lot en double et expiration.", "First_name_not_should_be_null_string": "Le pr�nom ne doit pas �tre une cha�ne nulle", "Last_name_not_should_be_null_string": "Le nom de famille ne doit pas �tre une cha�ne nulle", "Nurse_Id": "carte d'infirmi�re", "Dashboard_quicksight": "Tableau de bord", "fasting_blood_sugar": "Glyc�mie � jeun", "Postprandial_Blood_Sugar": "Glyc�mie post-prandiale", "Random_Blood_Sugar": "Glyc�mie al�atoire", "Date_Of_Birth": "fecha de nacimiento", "Mobile_Number": "M�vil", "Unit_Modify": "Configuration de l'unit�", "Height_Unit": "Unit� de hauteur", "Weight_Unit": "Unit� de poids", "User_already_exist": "User already exist", "health_number": "Health number", "health_address": "Health Address", "abha_address": "ABHA Adresse", "abha_number": "ABHA Num�ro", "Specialized_New_Consultation": "Nouvelle consultation sp�cialis�e", "Specialized_FollowUp_Consultation": "Consultation de suivi sp�cialis�e", "General_New_Consultation": "Nouvelle consultation g�n�rale", "General_FollowUp_Consultation": "Compl�ter la consultation totale", "Total_Complete_Consultation": "Complete Total Consultation", "Specialized_Complete_Consultation": "Consultation sp�cialis�e compl�te", "General_Complete_Consultation": "Consultation g�n�rale compl�te", "Choose_Days": "Choisissez le jour", "Follow_Up_days": "Jours de suivi", "Please_Enter_folloeUp_Days_In_Number": "Veuillez saisir le nombre de jours de suivi4", "MARK_AS_COMPLETE": "Marquer comme termin�", "MARK_AS_ACTIVE": "Marquer comme actif", "OTP_SENT": "Entrez l'OTP envoy� le", "VALIDATE_OTP": "Valider OTP", "LOGIN_CREDENTIALS_SENT_ON_REGISTERED_MAIL_ID": "Identifiants de connexion de l'utilisateur envoy�s le $", "PLEASE_PROVIDE_MAIL_ID": "Veuillez fournir un identifiant de messagerie", "PLEASE_PROVIDE_UID": "Veuillez donner UID", "YOU_ENTERED_INVALID_OTP": "<PERSON><PERSON> avez entr� invalide OTP", "SEND_OTP_ON_MAIL_TEMPLATE": "Cher $name, <br><br>OTP pour s'inscrire au syst�me de sant� num�rique ReMeDi est $OTP. Veuillez ne partager cet OTP avec personne.<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_BOOKED_TEMPLATE": "Cher/ch�re $patient_name, <br><br> Votre rendez-vous avec $Dr_name a �t� confirm� le $DATE � $TIME. Veuillez vous assurer d'�tre � l'heure au rendez-vous. Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_CANCELLED_TEMPLATE": "Cher/ch�re $patient_name, <br><br> Votre rendez-vous avec $Dr_name le $DATE � $TIME a �t� annul�. Merci! <br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_RESCHEDULED_TEMPLATE": "Cher/ch�re $patient_name, <br><br> Votre rendez-vous avec $Dr_name le $DATE1 � $TIME1 a �t� report� au $DATE2 � $TIME2. Veuillez vous assurer d'�tre � l'heure au rendez-vous. Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_CONSULTATION_COMPLETED_TEMPLATE": "Cher $patient_name, <br><br>Vous avez termin� votre consultation avec $Dr_name le $DATE � $TIME. Je vous souhaite un prompt r�tablissement! Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_USERNAME_PASSWORD_ON_EMAIL_AFTER_REGISTRATION_TEMPLATE": "Cher $patient_name, <br><br><PERSON><PERSON><PERSON> de vous �tre inscrit sur la plateforme de sant� num�rique ReMeDi�! Vous pouvez acc�der � votre compte en utilisant les identifiants suivants.<br><br>Nom d'utilisateur�", "SEND_EMAIL_NOTIFICATE_ON_CONSULTATION_COMPLETED_TEMPLATE_DOCTOR": "Cher $Dr, Vous avez termin� votre rendez-vous le $DATE � $TIME. Merci! <br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_RESCHEDULED_TEMPLATE_DOCTOR": "Cher/Ch�re $Dr, Le rendez-vous pr�vu le $DATE1 � $TIME1 avec vous a �t� reprogramm� le $DATE2 � $TIME2. Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_CANCELLED_TEMPLATE_DOCTOR": "Cher/Ch�re $Dr, Le rendez-vous pr�vu le $DATE � $TIME avec vous a �t� annul�. Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_BOOKED_TEMPLATE_DOCTOR": "Cher $Dr, Un rendez-vous a �t� confirm� avec vous le $DATE � $TIME. Merci d'�tre disponible � l'heure pour le rendez-vous. Merci�!<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "FollowUp_Complete_Consultation": "<PERSON>ivi termin�", "SEND_OTP_FORGOT_PASSWORD_ON_MAIL_TEMPLATE": "Cher $name, <br><br> Votre OTP pour la r�initialisation du mot de passe sur le syst�me de sant� num�rique ReMeDi est $OTP. Veuillez ne partager cet OTP avec personne.<br><br>Cordialement,<br>ReMeDi Digital Healthcare Team", "INVALID_EMAIL_ID": "El correo electr�nico no es v�lido", "OTP_Mail_has_been_successfully": "OTP a �t� envoy� avec succ�s par courrier.", "OTP_Sending_Mail_Failed": "�chec de l'envoi d'OTP par e-mail.", "YOU_HAVE_AN_APPOINTMENT_WITH_SAME_SLOT_WITH_ANOTHER_DOCTOR": "Tu as un rendez-vous au m�me cr�neau avec un autre m�decin. Choisis un autre emplacement", "INVALID_HEIGHT": "<PERSON><PERSON> invalide", "PLEASE_SELECT_PATIENT_STATUS": "Veuillez s�lectionner l'�tat du patient", "second_Configuration": "Configuration de la minuterie de consultation", "please_enter_seconds(b/w_0_to_999)": "Veuillez entrer les secondes n/b de 0 � 999.", "Diabetes": "Diab�te", "Hypertension": "Hypertension", "Both": "Les deux", "Normal": "Normal", "Select_Status": "S�lectionnez le statut", "timeOffset": "TimeOffset(UTC)", "Consultation_Detail": "D�tails de la consultation", "patient_wise_report": "Rapport ax� sur les patients", "Patient_Wise_Consultation_Report": "Informe de consulta inteligente para el paciente", "Total_Slot": "Emplacement total", "Available_Slot": "Emplacement disponible", "Used_Slot": "Emplacement utilis�", "Slot_Detail": "D�tail de l'emplacement", "Doctor_Wise_Slot_Details": "D�tails de la machine � sous Doctor Wise", "Cancelled_by_patient": "<PERSON><PERSON> le <PERSON>", "Patient_Name_MIS": "Nom Pat<PERSON>", "Age_MIS": "�ge", "Mobile_MIS": "T�l�phone", "DOB_MIS": "Date de naissance", "Password_rule": "Le mot de passe doit contenir au moins 8 caract�res, doit contenir au moins 1 majuscule, 1 minuscule, 1 num�rique et 1 caract�re sp�cial", "File_size_should_be_less_than_5MB": "File size should be less than 5MB.", "consent1": "Vous pouvez cr�er instantan�ment votre num�ro ABHA en utilisant Aadhaar. Veuillez vous assurer qu'A<PERSON>ha<PERSON> est li� � un num�ro de mobile car une authentification OTP suivra. Si vous n'avez pas de num�ro de mobile associ�, visitez l'�tablissement participant � l'ABDM le plus proche et demandez de l'aide.", "consent2": "Je partage volontairement mon num�ro Aadhaar/identifiant virtuel d�livr� par l'Autorit� d'identification unique de l'Inde (� UIDAI �) et mes informations d�mographiques dans le but de cr�er un num�ro de compte de sant� Ayu<PERSON><PERSON> Bharat (� num�ro ABHA �) et un compte de sant� Ayushman Bharat. adresse (� Adresse ABHA �). J'autorise NHA � utiliser mon num�ro Aadhaar / identifiant virtuel pour effectuer une authentification bas�e sur Aadhaar avec UIDAI conform�ment aux dispositions de la loi Aadhaar (livraison cibl�e de subventions, avantages et services financiers et autres), 2016 aux fins susmentionn�es. Je comprends qu'UIDAI partagera mes d�tails e-KYC ou ma r�ponse � Oui � avec NHA une fois l'authentification r�ussie.", "consent3": "J'accepte l'utilisation de mon adresse ABHA et de mon num�ro ABHA pour relier mes dossiers de sant� h�rit�s (pass�s) et ceux qui seront g�n�r�s lors de cette rencontre.", "consent4": "J'autorise le partage de tous mes dossiers de sant� avec un ou plusieurs prestataires de soins de sant� dans le but de me fournir des services de soins de sant� lors de cette rencontre.", "consent5": "J'accepte l'anonymisation et l'utilisation ult�rieure de mon dossier m�dical � des fins de sant� publique.", "consent6": "Je, (nom du professionnel de sant� - en fonction du nom d'utilisateur utilis� pour vous connecter au syst�me), confirme avoir d�ment inform� et expliqu� au b�n�ficiaire le contenu du consentement aux fins susmentionn�es.", "consent7": "<PERSON><PERSON><PERSON><PERSON>, (nom du b�n�ficiaire), re�u des explications sur le consentement indiqu� ci-dessus et donne par la pr�sente mon consentement aux fins susmentionn�es.", "Create_ABHA_Number": "Cr�er un num�ro ABHA", "Using_Aadhaar": "Util<PERSON>", "Enter_Aadhaar_Number": "Entrez le num�ro Aadhaar", "Generate_OTP": "G�n�rer un OTP", "ABHA_Verification": "V�rification ABHA (pour les utilisateurs ABHA existants)", "Enter_ABHA_number": "Entrez le num�ro/l'adresse ABHA", "InvalidCaptcha": "<PERSON><PERSON> <PERSON><PERSON>", "Please_enter_captcha": "Veuillez saisir un captcha", "Consent8": "Je d�clare par la pr�sente que", "Enter_valid_number": "Por favor ingresa un numero valide", "Health_number_address": "N/A", "New_Consent_Request": "Nouvelle demande de consentement", "M": "M", "If_other_problem_or_concern_please_explain": "Si autre probl�me ou pr�occupation, veuil<PERSON>z expliquer", "m": "m", "cancer": "cancer", "ABHA_Address": "Adresse ABHA", "Medical_Test_2_done_at_Center": "Test m�dical 2 effectu� au centre", "Full_name": "Nom complet", "Your_Profile_Details": "D�tails de votre profil", "DENGUE_IGG_IGM": "DENGUE IGG IGM", "Random": "Al�atoire", "unique_PID": "ID unique", "reviewed_by_doctor": "Revu par le m�decin", "Consult_Fee_To_be_Return": "Honoraires de consultation � rembourser", "Foot_Report": "Rapport du pied", "oauth_Url_is_NULL": "L'URL oauth est NULL", "MultiCare_ManualEntry": "<PERSON><PERSON> man<PERSON>le MultiCare", "Please_enter_your_mobile_number": "Veuillez saisir votre num�ro de t�l�phone portable", "Changing_Password_Rule_Not_Matching": "La r�gle de changement de mot de passe ne correspond pas", "Congratulations": "F�licitations", "Required_data_not_provided": "<PERSON><PERSON> requises non fournies", "Aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "MulticareProfile": "Profil Multicare", "Medical_Test_8_done_at_Center": "Test m�dical 8 effectu� au centre", "Medical_Test_1_done_at_Center": "Test m�dical 1 effectu� au centre", "pgender": "Genre", "Postprandial": "Postprandial", "Inqueue": "En file d'attente", "As_per_the_doctor_prescription_Did_you_take_medicine?": "<PERSON><PERSON> l'ordonnance du m�decin, avez-vous pris des m�dicaments ?", "Medical_Test_3_done_at_Center": "Test m�dical 3 effectu� au centre", "ABDM_HIU": "ABDM_HIU", "Medical_Test_4_done_at_Center": "Test m�dical 4 effectu� au centre", "Medical_Test_5_done_at_Center": "Test m�dical 5 effectu� au centre", "Suggestions": "Suggestions", "Link_ABHA_Address": "Lien vers l'adresse ABHA", "store_id": "ID du magasin", "Medical_Test_7_done_at_Center": "Test m�dical 7 effectu� au centre", "Medical_Test_6_done_at_Center": "Test m�dical 6 effectu� au centre", "How_did_you_find_cleanliness_and_other_facilities_like_water_toilets_etc_at_the_center?": "Comment avez-vous trouv� la propret� et les autres installations comme les toilettes d'eau, etc. au centre ?", "Foot_Examination": "Examen des pieds", "password_Exists": "Le mot de passe existe", "Foot_Results": "R�sultats des pieds", "If_yes_then_which_all_tests_were_done?": "Si oui, quels tests ont �t� effectu�s ?", "Validate_using": "Validation en utilisant", "is_FollowUp_Val": "<PERSON><PERSON><PERSON>", "As_per_Aadhaar": "<PERSON><PERSON>", "patient_Info": "Informations sur le patient", "Patient_Not_Exist_For_Given_Mobile": "Le patient n'existe pas pour le mobile donn�", "Fasting": "<PERSON><PERSON>", "File_name_is_required": "Le nom du fichier est requis", "Verify_OTP": "V�rifier OTP", "File_size_should_be_less_than_2MB": "La taille du fichier doit �tre inf�rieure � 2 Mo", "Date_of_1st_Examination": "Date de la premi�re examination", "Multicare_ManualEntry": "Entr�e manuelle Multicare", "patient_Profile": "<PERSON><PERSON> du <PERSON>", "Date_of_2nd_Examination": "Date de la deuxi�me examination", "Date_of_5th_Examination": "Date de la cinqui�me examination", "Foot_Secure_Report": "Rapport s�curis� des pieds", "same_As_Old_Password": "M�me que l'ancien mot de passe", "Download_ABHA_card": "T�l�charger la carte ABHA", "If_you_stop_taking_any_medications_in_the_PAST_SIX_MONTHS_then_Medication_name?": "Si vous avez arr�t� de prendre des m�dicaments au cours des SIX DERNIERS MOIS, alors quel est le nom du m�dicament ?", "If_not_then_why?": "Sinon, pourquoi ?", "pat_Name": "Nom du patient", "Invalid_file_extension": "Extension de fichier non valide", "Problem_in_Location_Center": "Probl�me au centre de localisation", "Authenticate_your_ABHA_number": "Authentifier votre num�ro ABHA", "Date_of_4th_Examination": "Date de la quatri�me examination", "OTP_has_been_sent": "OTP a �t� envoy�", "Date_of_3rd_Examination": "Date de la troisi�me examination", "ABHA_PATIENT_REGISTRATION_QUEUE": "Patient <PERSON>", "ISABHAREG": "ABHA Patient Registration", "CREATE_PATIENT_ABHA": "Create New Record", "ABDM_RECORD": "ABDM Record", "User_Details_Updated": "Les d�tails de l'utilisateur ont �t� mis � jour.", "DESK_CONFIGURATION": "Configuration du bureau", "DESK_NAME": "Nom du bureau", "DESK_NUMBER": "Num�ro de bureau", "DESK_DESCRIPTION": "Description du bureau", "GROUPING": "Regroupement", "SELECT_DESK": "S�lectionnez le bureau", "PLEASE_SELECT_DESK": "Veuillez s�lectionner un bureau", "PLEASE_ENTER_UNIQUE_ID": "Please Enter Unique Id", "YOU_ALREADY_HAVE_APPOINTMENT_ON_SAME_DAY": "This patient already has an appointment for the same day.", "DESK_STATUS": "Desk Status", "SEND_TO_DOCTOR": "Send To Doctor", "SEND_TO_DESK": "Send To Desk", "CANNOT_START_IN_CONSULTATION_APPOINTMENT": "Patient is in Consultation with <PERSON>. You can not start this consultation.", "YOU_HAVE_ACTIVE_SUBSCRIPTION": "You have active subscription plan!", "SUBSCRIPTION_PLAN_DETAILS": "Subscription Plan Details", "PLAN_NAME": "Plan Name", "REMAINING_CONSULTATIONS": "Remaining Free Consultation", "ALLOCATED_CONSULTATION": "Allocated Free Consultation", "PLAN_EXPIRY_DATE": "Plan expiry date", "YOU_DO_NOT_HAVE_ACTIVE_PLAN": "You do not have any active subscription plan!", "YOU_CAN_NOT_START_SENT_CONSULTATIONS": "Consultation is sent to the Doctor. You can not start this consultation.", "YOUR_SUBSCRIPTION_PLAN_IS_EXPIRED": "Your current subscription plan has expired. Please renew your subscription to continue using the service.", "CHARGES_MAY_APPLY": "If you choose to continue without renewal, charges may apply.", "REFERRALS_MASTER_LIST_CONFIGURATION": "Referrals Master List Configuration", "HOSPITAL_NAME": "Hospital Name", "HOSPITAL_CODE": "Hospital Code", "HOSPITAL_ADDRESS": "Hospital Address", "HOSPITAL_MAIL_ID": "Hospital Email Id", "HOSPITAL_CONTACT_NUMBER": "Hospital Contact Number", "SEND_EMAIL_REFFERAL_TEMPLATE": "Dear $to_dr_name,<br/>This email serves as a referral for $patient_name, a patient of $referring_dr_name at Jubicare Smart Clinics.<br/> $patient_name with $unique_code is presenting with $remark. We have attached $referring_dr_name consultation notes for your reference. <br/> We believe that your expertise would be of great benefit to $patient_name.<br/> Our clinic contact information is also provided below for any questions you may have regarding the patient's medical history. <br/><br/> <b>Jubicare Smart Clinics</b><br/> Phone number- **********<br/> Email address- <EMAIL><br/>Thank you for your time and consideration. We look forward to collaborating with you to provide optimal care. <br/><br/>Sincerely,<br/>$referring_dr_name<br/>Jubicare Smart Clinics", "SEND_EMAIL_REFFERAL_SUBJECT": "Referral for Patient $patient_name from Jubicare Smart Clinics", "REMARKS_FIELD_NOT_EMPTY": "Le champ Remarques ne doit pas �tre vide", "REMARKS": "<PERSON><PERSON><PERSON>", "DOCTOR_EMAIL_ID": "Doctor <PERSON><PERSON>", "DOCTOR_CONTACT_NUMBER": "Doctor Contact Number", "FAMILY_CARD_ID": "Identifiant de la carte familiale", "HEAD_OF_HOUSEHOLD": "Chef de famille", "UNIQUE_ID": "Identifiant unique", "SUBCRIPTION_PLAN_NAME": "Nom du plan d'abonnement", "SUBSCRIPTION_PLAN_ISSUED_DATE": "Date d'�mission du plan d'abonnement", "SUBSCRIPTION_PLAN_EXPIRY_DATE": "Date d'expiration du plan d'abonnement", "ALLOCATED_FREE_CONSULTATIONS": "Consultations gratuites attribu�es", "USED_FREE_CONSULTATIONS": "Consultations gratuites utilis�es", "REMAINING_FREE_CONSULTATIONS": "Consultations gratuites restantes", "NAME_OF_FAMILY_MEMBER": "Nom du membre de la famille", "CONSULTATION_DATE": "Date de consultation", "FAMILY_MEMBER_WISE_REPORT": "Rapport par membre de la famille", "POWERED_BY": "Aliment� par", "CONSULTATION_ID": "Num�ro de consultation", "CONSULTATION_FEE": "Frais de consultation", "FREE": "GRATUITE", "UNIQUE_CODE": "Code unique", "QUEUE_NUMBER": "Num�ro de file d'attente", "CONSULTATION_INVOICE": "Facture de consultation", "OTP_send_to_linked_mobile_number": "OTP envoy� au num�ro de mobile li�", "ADD_MEDICINE": "Ajouter un m�dicament", "NO_OF_DAYS_MEDICATION": "Nombre de jours", "INVALID_ABHA_NUMBER": "Invalid Health Number", "INVALID_ABHA_ADDRESS": "Invalid Health Address", "PROVIDE_PATIENT_ID_UNIQUE_ID": "Please provide patient name or Unique id", "CLEAR": "Clear", "client-id-client-secret-config": "FHIR Client-Id & Client-Secret Config", "client-id-client-secret-generate-btn": "Generate Client-Id & Client-Secret", "Privacy_Policy": "Politique de confidentialit�", "SELECT_VERIFICATION_METHOD": "S�lectionnez la m�thode de v�rification", "SELECT_VALIDATION_METHOD": "S�lectionnez le Valider", "PROCEED": "<PERSON><PERSON>�<PERSON>", "ENTER": "<PERSON><PERSON><PERSON> le", "OTP_send_to_aadhaar_linked_mobile_number": "OTP envoy� au num�ro de mobile li� � Aadhaar", "INVALID_MOBILE_NUMBER": "Num�ro de portable invalide", "BLOOD_GROUPING": "GROUPEMENT SANGUIN", "A_POSITIVE_(A+)": "A POSITIF (A+)", "A_NEGATIVE_(A-)": "A N�GATIF (A-)", "B_POSITIVE_(B+)": "B POSITIF (B+)", "B_NEGATIVE_(B-)": "B N�GATIF (B-)", "AB_POSITIVE_(AB+)": "AB POSITIF (AB+)", "AB_NEGATIVE_(AB-)": "AB N�GATIF (AB-)", "O_POSITIVE_(O+)": "O POSITIF (O+)", "O_NEGATIVE_(O-)": "O N�GATIF (O-)", "GeneralExaminationCamera": "Cam�ra d'examen g�n�ral", "General_Examination_Camera": "Cam�ra d'examen g�n�ral", "param_GeneralExaminationCamera": "Cam�ra d'examen g�n�ral", "PLEASE_SELECT_DEVICE": "Veuillez s�lectionner l'appareil", "GlUCONAVII_PRO": "GlucoNavii PRO", "LIPIDOCARE_WIRELESS": "LipidoCare (Sans fil)", "LIPIDOCARE_CABLE": "LipidoCare (C�ble)", "LIPIDOCARE_WIRELESS_INSTRUCTION_I": "Assurez-vous que votre appareil est � proximit�.", "LIPIDOCARE_WIRELESS_INSTRUCTION_II": "Allumez l'appareil.", "LIPIDOCARE_WIRELESS_INSTRUCTION_III": "Maintenez le bouton d'alimentation enfonc� pour activer le mode de couplage Bluetooth.", "LIPIDOCARE_WIRELESS_INSTRUCTION_IV": "�teignez l'appareil apr�s avoir re�u le message 'OK' sur l'�cran.", "LIPIDOCARE_WIRELESS_INSTRUCTION_V": "Maintenez le bouton d'alimentation enfonc� lorsque l'appareil est �teint pour obtenir la lecture.", "LIPIDOCARE_DEVICE_UNREACHABLE": "Appareil LipidoCare inaccessible. Assurez-vous qu'il est � proximit� et reprenez la lecture.", "IS_LIPIDOCARE_DEVICE_PAIRED": "Des lectures ont-elles d�j� �t� enregistr�es avec cet appareil ?", "CONFIRM_ACTION": "Confirmer l'action", "PLEASE_CONFIGURE_PHONE_NO_LENGTH": "Veuillez configurer la longueur du num�ro de t�l�phone.", "SICKLE_CELL": "Dr�panocytose", "Result_is_Other_Hemoglobinopathies_or_Thalassemia_or_Other_Hemoglobinopathies_Associated_With_Thalassemia": "Autres h�moglobinopathies ou thalass�mie ou autres h�moglobinopathies associ�es � la thalass�mie", "Result_is_Normal": "Le r�sultat est normal", "Result_is_Sickle_Cell_Disease_(SCD)_/_Sickle_Cell_With_Other_Hemoglobinopathie": "Dr�panocytose / Dr�panocytose avec d'autres h�moglobinopathies", "Result_is_Sickle_Cell_Trait_or_Its_Association_With_Other_Hemoglobinopathies": "<PERSON><PERSON><PERSON> dr�panocytaire ou son association avec d'autres h�moglobinopathies", "Doctor_has_been_Disabled_for": "Le m�decin est invalide depuis", "Doctor_has_been_Enabled_for": "Le m�decin a �t� activ� pour", "Invalid_file_type_Please_select_an_image_(jpg,jpeg,or,png)": "Type de fichier invalide. Veuillez s�lectionner une image (jpg, jpeg ou png)", "Disabled_successfully": "D�sactiv� avec succ�s", "Enabled_successfully": "Activ� avec succ�s", "Consent_Request_Initiated": "De<PERSON>e de consentement lanc�e", "HbA1c_BLE": "HbA1c (Sans Fil)", "Please_Switch_On_HbA1c_BLE_Device": "Veuillez allumer l'appareil HbA1c (Sans Fil)", "para_HBA1CBLE": "HBA1CBLE", "HBA1CBLE_ManualEntry": "Entr�e Manuelle HBA1C", "Parameter_has_been": "Le param�tre a �t�", "Domain_Pharmacy_has_been": "Le domaine pharmacie a �t�", "Domain_created_successfully": "Domaine cr�� avec succ�s", "Domain_has_been": "Le domaine a �t�", "ABDM_CARECONTEXT_LIST": "Liste de contexte de soins ABDM", "REFERENCE_NO": "Num�ro de r�f�rence", "RECORD_TYPE": "Type d'enregistrement", "RECORD_DATE": "Date d'enregistrement", "RECORD_LINKED": "Enregistrement li�", "WBCDIFF": "Diff�rentiel des globules blancs", "para_WBCDIFF": "WBCDIFF", "param_WBCDIFF": "Diff�rentiel des globules blancs", "WBCDIFF_ManualEntry": "<PERSON><PERSON> manuelle du diff�rentiel des globules blancs", "WBC": "WBC", "Total_WBC_Count": "Total WBC Count", "NEUTROPHILS": "NEUTROPHILS", "LYMPHOCYTES": "LYMPHOCYTES", "MONOCYTES": "MONOCYTES", "EOSINOPHILS": "EOSINOPHILS", "BASOPHILS": "BASOPHILS", "FAILED_TO_SAVE_WBC_DIFF_READING": "Impossible de sauvegarder la lecture diff�rentielle de WBC. Veuillez r�essayer.", "SUCCESSFULLY_SAVED_WBCDIFF_DATA": "Donn�es diff�rentielles des leucocytes enregistr�es avec succ�s.", "ophthalmoscope_access": "Acc�s � l'Ophthalmoscope", "PLEASE_ENTER_NATIONAL_ID": "Entrez un identifiant unique", "Please_Provide_National_Id_first": "Veuillez fournir une pi�ce d'identit� nationale.", "Please_Provide__Valid_National_Id": "Veuillez fournir une pi�ce d'identit� nationale valide.", "OTP_verified_success": "Succ�s v�rifi� par OTP", "X-Ray": "X-Ray", "X_Ray_Taken": "Radiographie effectu�e", "X_Ray_Fees": "Frais de radiographie", "X-Ray_Viewer": "Visor de rayos X", "X-Ray_REPORT_NOT_AVAILABLE": "El informe no est� disponible para descargar.", "X-Ray_ALREADY_PRESCRIBED": "Esta radiograf�a ya ha sido prescrita. Para agregarla nuevamente, por favor ve a 'Investigaciones', elim�nala y luego a��dela aqu�.", "This_User_Already_Exists": "Ce compte existe d�j�", "View_Profile": "Voir le profil", "ADD_ABHA_Address": "Ajouter une adresse ABHA", "TemperatureASHA": "Term�metro (ASHA+)", "param_TemperatureASHA": "Thermometer(ASHA+)", "editprofile_bookappointment": "Modifier le profil et prendre rendez-vous", "national_id": "Carte d'identit� nationale", "Stethoscope_ASHA": "St�thoscope (ASHA+)", "param_StethoscopeASHA": "St�thoscope(ASHA+)", "FetosenseFetalDoppler": "Doppler fetal(Fetosense)", "param_FetosenseFetalDoppler": "Doppler fetal(Fetosense)", "ECG_INTERPRETATION_REPORT_GENERATION_FAILED": "Error en la generaci�n del informe de interpretaci�n de ECG. Por favor, int�ntelo de nuevo.", "Reset_Dates": "R�initialiser les dates", "PLEASE_WAIT_ECG_INTERPRETATION_DATA_SENDING_IN_PROGRESS": "Por favor, espere, estamos enviando el ECG para su interpretaci�n.", "REPORT_DOWNLOAD_NOT_AVAILABLE": "El informe no est� disponible para descargar.", "Enter_Valid_FEV1_FVC_rate": "Enter Valid FEV1/FVC Rate", "Enter_Valid_FEF25_75_rate": "Enter Valid FEF25_75 Rate", "Enter_Valid_FET_rate": "Enter Valid FET Rate", "Enter_Valid_FIVC_rate": "Enter Valid FIVC Rate", "Enter_Valid_PIF_rate": "Enter Valid PIF Rate", "Spiro(FEV1_FVC)": "Spiro (FEV1/FVC)", "Spiro(FEF25_75)": "Spiro (FEF25-75%)", "Spiro(FET)": "<PERSON><PERSON><PERSON> (FET)", "Spiro(FIVC)": "Spiro (FIVC)", "Spiro(PIF)": "Spiro (PIF)", "XAXIS_TIME": "Time", "Volume_Flow": "Volume-Flow", "Volume_Time": "Volume-Time", "VALID_ECG_MANUAL_ENTRY": "Al menos un campo debe contener un n�mero v�lido.", "INVALID": "Invalid", "error_while_taking_device_reading": "L\\u2019appareil a �t� d�connect� pendant la lecture. Veuillez l\\u2019�teindre, le reconnecter et r�essayer.", "Please_select_a_reading_first": "<PERSON><PERSON><PERSON><PERSON> d'abord s�lectionner une lecture", "show_nurse_detail_in_report": "Enable nurse details in report", "spiro_sensor_disconnected_successfully": "La lecture n\\u2019a pas pu �tre termin�e. Red�marrez l\\u2019appareil et r�essayez.", "Please_enter_referral_note": "Veuillez saisir la note de r�f�rence", "Enter_Referral_Note": "<PERSON><PERSON> la note de r�f�rence", "Please_enter_the_referral_note_within_the_allowed_limit": "Veuillez saisir la note de r�f�rence dans la limite autoris�e", "Enter_Note": "<PERSON><PERSON> une note", "Referral_Note": "Note de r�f�rence", "Date_Time": "Date et Heure", "Parameter_Notes": "Remarques sur les param�tres", "stetho_doctor_Note": "<PERSON><PERSON><PERSON>", "no_Reading": "Pa<PERSON> de lecture", "Device": "Appareil", "Reading_taken": "Lecture prise", "Devices_Used": "Appareils utilis�s", "reading_completed_msg": "Le test a �t� effectu� avec succ�s. Le capteur du spirom�tre s\\u2019�teindra automatiquement.", "ERR_DEVICE_DISCONNECT_MIDTEST": "L\\u2019appareil a �t� d�connect� au milieu du test. Veuillez le reconnecter et r�essayer.", "DONGLE_DISCONNECT_INBETWEEN": "Cl� USB non d�tect�e. Veuillez reconnecter la cl� et r�essayer de prendre la lecture.", "cdss_module_active": "CDSS Module Active", "CDSS_Suggestion_Btn": "Fetch CDSS Advice", "Risk_Factors_And_Personal_Habits": "Lifestyle & Health Risk Assessment", "Tobacco_Smoking": "Do you use tobacco (smoking)?", "Alcohol": "Do you consume alcohol?", "Routine_Physical_Exercise": "Do you exercise regularly?", "On_Medication": "Are you currently on any medication?", "Get_CDSS_Medicine": "Fetch CDSS Medicine", "Submit_CDSS_feedback": "Submit CDSS Feedback", "Feedback_report": "Recommendation Summary", "HbA1C_Mandatory_If_FBS": "When HbA1c is entered, Fasting Blood Sugar (FBS) reading is mandatory."}