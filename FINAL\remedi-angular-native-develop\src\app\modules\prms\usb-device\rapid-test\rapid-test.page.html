<div class="rapid-test-page">
  <!-- Header (keeping your existing header) -->
  <div class="rapid-test-header">
    <div class="header-content">
      <h1 class="page-title">{{ 'RapidTest' | translate }}</h1>
      <button class="close-btn" (click)="onDialogClose()">
        <span class="close-icon">×</span>
      </button>
    </div>
  </div>

  <!-- Instructions Content - Show only when showInstructions is true -->
  <div class="rapid-test-content1" *ngIf="showInstructions">
    <!-- Instructions List -->
    <div class="instructions-container">
      <p class="instruction-header">{{ 'Please_follow_steps' | translate }}:</p>
      <div class="instruction-list">
        <div class="instruction-item">
          <span class="step-number">1.</span>
          <span class="step-text">{{ 'Connect_optical_reader' | translate }}</span>
        </div>
        <div class="instruction-item">
          <span class="step-number">2.</span>
          <span class="step-text">{{ 'Insert_tray_in_optical_device' | translate }}</span>
        </div>
        <div class="instruction-item">
          <span class="step-number">3.</span>
          <span class="step-text">{{ 'Switch_on_device' | translate }}</span>
        </div>
        <div class="instruction-item">
          <span class="step-number">4.</span>
          <span class="step-text">{{ 'Select_Test_from_dropdown' | translate }}</span>
        </div>
        <div class="instruction-item">
          <span class="step-number">5.</span>
          <span class="step-text">{{ 'Click_the_Start_Test_button' | translate }}</span>
        </div>
      </div>
    </div>

    <!-- Test Selection -->
    <div class="test-selection-container">
      <div class="test-select-row">
        <label class="select-label">{{ 'Please_Select_Test' | translate }}:</label>
        <select 
          [(ngModel)]="testName" 
          (change)="onTestSelects($event)" 
          class="test-selector">
          <option value="">Choose a test</option>
          <option value="HEPA_SCAN_HBsAg">{{ 'HEPA_SCAN_HBsAg' | translate }}</option>
          <option value="HEPA_SCAN_HCV">{{ 'HEPA_SCAN_HCV' | translate }}</option>
          <option value="SYPHILIS">{{ 'SYPHILIS' | translate }}</option>
          <option value="TROPONIN_I">{{ 'TROPONIN_I' | translate }}</option>
          <option value="MALERIA_P.f_P.v">{{ 'MALERIA_P.f_P.v' | translate }}</option>
          <option value="MALERIA_P.f_PAN">{{ 'MALERIA_P.f_PAN' | translate }}</option>
          <option value="DENGUE_NS1">{{ 'DENGUE_NS1' | translate }}</option>
          <option value="DENGUE_IGG_IGM(Bhat_Biotech)">{{ 'DENGUE_IgG_IgM(Bhat_Biotech)' | translate }}</option>
          <option value="DENGUE_IGG_IGM(SD_Standard)">{{ 'DENGUE_IgG_IgM(SD_Standard)' | translate }}</option>
          <option value="PREGNANCY_HCG">{{ 'PREGNANCY_HCG' | translate }}</option>
          <option value="HIV_TRILINE">{{ 'HIV_TRILINE' | translate }}</option>
          <option value="BLOOD_GROUPING">{{ 'BLOOD_GROUPING' | translate }}</option>
          <option value="Sickel_Cell">{{ 'Sickel_Cell' | translate }}</option>
        </select>
      </div>
    </div>

    <!-- Error Message -->
    <div class="error-message" *ngIf="errorMessage" [innerHTML]="errorMessage"></div>
  </div>

  <!-- Footer with Start Test Button -->
  <div class="rapid-test-footer" *ngIf="showInstructions">
    <div class="footer-line"></div>
    <div class="footer-content">
      <button 
        class="start-test-btn" 
        (click)="strarRapidTest()" 
        [disabled]="!testName">
        {{ 'StartTest' | translate }}
      </button>
    </div>
  </div>

  <!-- Test in Progress - Show when test is running -->
  <div class="rapid-test-content2" *ngIf="!showInstructions && !resultImage">
    <div class="loading-container">
      <div class="loading-card">
        <div class="card-content">
          <div class="loading-content">
            <div class="loading-text">
              <h3>
                {{ displayResponse || 'Computing result...' }}
                <span *ngIf="showLoadingDots" class="loading-dots">
                  <span class="dot"></span>
                  <span class="dot"></span>
                  <span class="dot"></span>
                </span>
              </h3>
              <p *ngIf="displayMessage" class="error-text">{{ displayMessage }}</p>
              <!-- API Response Messages -->
              <div *ngIf="apiSuccessMessage" class="success-message">{{ apiSuccessMessage }}</div>
              <div *ngIf="apiErrorMessage" class="api-error-message">{{ apiErrorMessage }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Result - Show when test is complete -->
  <div class="rapid-test-content3" *ngIf="!showInstructions && resultImage">
    <div class="result-section">
      <div class="result-image-card">
        <div class="card-header">
           <div *ngIf="apiSuccessMessage" class="success-message">{{ apiSuccessMessage }}</div>
          <div *ngIf="apiErrorMessage" class="api-error-message">{{ apiErrorMessage }}</div>
          <h2 class="card-title">{{ displayResponse }}</h2>
          <!-- API Response Messages in Result Section -->
          <!-- <div *ngIf="apiSuccessMessage" class="success-message">{{ apiSuccessMessage }}</div>
          <div *ngIf="apiErrorMessage" class="api-error-message">{{ apiErrorMessage }}</div> -->
        </div>
        <div class="card-content">
          <div class="image-container">
            <img [src]="img" alt="Rapid Test Result" class="result-image" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer with Save Button for Results -->
    <div class="rapid-test-footer">
      <div class="footer-line"></div>
      <div class="footer-content">
        <button 
          *ngIf="showSaveButton"
          class="save-result-btn" 
          (click)="saveHIVTest()"
          [disabled]="isSaving">
          <span *ngIf="!isSaving">{{ 'Save' | translate }}</span>
          <span *ngIf="isSaving">{{ 'Saving' | translate }}...</span>
        </button>
      </div>
    </div>
  </div>
</div>