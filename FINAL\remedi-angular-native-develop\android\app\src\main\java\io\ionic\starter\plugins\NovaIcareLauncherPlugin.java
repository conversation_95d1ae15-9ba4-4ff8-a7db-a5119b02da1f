package io.ionic.starter.plugins;

import android.content.Intent;
import android.content.ComponentName;
import android.util.Log;

import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.JSObject;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.ActivityCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;

@CapacitorPlugin(name = "NovaIcareLauncherPlugin")
public class NovaIcareLauncherPlugin extends Plugin {

  public static NovaIcareLauncherPlugin instance;
  public PluginCall savedCall;  // store call in case app reloads

  @Override
  public void load() {
    instance = this;
  }

  public void handleStethoResultManually(int requestCode, int resultCode, Intent data) {
    if (savedCall != null) {
      handleDeviceResult(savedCall, resultCode, data);
      savedCall = null;
    } else {
      Log.w("NovaIcare", "No saved PluginCall found for manual result handling");
    }
  }
  @PluginMethod
  public void launchDeviceWithResult(PluginCall call) {
    String deviceType = call.getString("deviceType");
    Log.d("NovaIcare", "🚀 Launching device: " + deviceType);

    if (deviceType == null || deviceType.isEmpty()) {
      call.reject("Missing deviceType");
      return;
    }

    // Convert to uppercase with underscores if needed
    deviceType = deviceType.toUpperCase(); // e.g., "pulse_oximeter" → "PULSE_OXIMETER"

    this.savedCall = call;

    try {
      Intent intent = new Intent();
      intent.putExtra("USE_SENSOR", true);
      intent.putExtra("class_name", call.getString("class_name"));
      intent.putExtra("useflag", "0");
      intent.putExtra("package_name", call.getString("package_name"));
      intent.putExtra("language", call.getString("language"));
      intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      // You can add more parameters here if required (eg. optical reader)
      // // intent.putExtra("", call.getString(""))


       if (deviceType.equals("OPTICAL_READER")) {
            String testType = call.getString("testType");
            intent.putExtra("test_type", testType);
            intent.putExtra("test_type_fr", testType);
        }
       
      // Log.d("NovaIcare22rakesh", "🚀 Launching device: " + deviceType);

      //  if (deviceType.equals("OPTICAL_READER")) {
      //       String testType = call.getString("testType");
      //       intent.putExtra("test_type", testType);
      //       intent.putExtra("test_type_fr", testType);
      //   }
      //  String deviceType3 = call.getString("deviceType");
      //  Log.d("NovaIcare33rakesh", "🚀 Launching device: " + deviceType3);

      String realId = call.getString("real_id");
      if (realId == null || realId.isEmpty()) {
        intent.putExtra("pid", call.getString("patient_id"));
      } else {
        intent.putExtra("pid", realId);
      }

      // Normalize deviceType (e.g., "pulse_oximeter" → "PULSE_OXIMETER")
      // String normalizedType = deviceType.toUpperCase(); // or adjust as needed
      // Log.d("NovaIcare", "🔍 Normalized deviceType: " + normalizedType);

      // Set device-specific ComponentName
      ComponentName component = getDeviceComponent(deviceType);
      if (component == null) {
        call.reject("Unsupported device type: " + deviceType);
        return;
      }
      deviceType = deviceType.toUpperCase();
      intent.setComponent(component);

      startActivityForResult(call, intent, "handleDeviceResult");

    } catch (Exception e) {
      Log.e("NovaIcare", "❌ Error launching device: " + deviceType, e);
      call.reject("Failed to launch " + deviceType, e);
    }
  }

  //   @PluginMethod
  // public void launchStethoWithResult(PluginCall call) {
  //   String deviceType = call.getString("deviceType");
  //   if (deviceType == null || deviceType.isEmpty()) {
  //     call.reject("Missing deviceType");
  //     return;
  //   }

  //   // Convert to uppercase with underscores if needed
  //   deviceType = deviceType.toUpperCase(); // e.g., "pulse_oximeter" → "PULSE_OXIMETER"

  //   Log.d("deviceType initiated -", deviceType);

  //   call.getData().put("deviceType", deviceType);
  //   launchDeviceWithResult(call);
  // }

  private ComponentName getDeviceComponent(String deviceType) {
    deviceType = deviceType.toUpperCase();
    Log.d("deviceType inti - ", deviceType);
    switch (deviceType) {
      case "PULSE_OXIMETER":
        return new ComponentName("com.neurosynaptic.nova_icare",
          "com.neurosynaptic.ble.sensors.PulseOxiMeterSensor1");
      case "THERMOMETER":
        return new ComponentName("com.neurosynaptic.nova_icare",
          "com.neurosynaptic.ble.sensors.TemperatureSensor1");
      case "BLOOD_PRESSURE":
        return new ComponentName("com.neurosynaptic.nova_icare",
          "com.neurosynaptic.ble.sensors.BloodPressureSensor1");
      case "STETHOSCOPE":
        return new ComponentName("com.neurosynaptic.nova_icare",
          "com.neurosynaptic.usb.StethoSensor");
      case "ECG":
        return new ComponentName("com.neurosynaptic.nova_icare",
          "com.neurosynaptic.bluetooth.sensors.ECGSensor1");

       case "OPTICAL_READER":
            return new ComponentName("com.neurosynaptic.nova_icare",
           "com.neurosynaptic.usb.Optical_Reader" );


      case "HEMOGLOBIN":
        return new ComponentName("com.neurosynaptic.nova_iassist",
          "com.neurosynaptic.usb.HemoglobinSensor");

      //ADD MORE DEVICES
      default:
        Log.w("NovaIcare", "Unknown device type: " + deviceType);
        return null;
    }
  }

  @ActivityCallback
  private void handleDeviceResult(PluginCall call, int resultCode, Intent data) {
    handleResult(call, resultCode, data);
  }

  @ActivityCallback
  private void handleResult(PluginCall call, int resultCode, Intent data) {
    try {
      Log.d("NovaIcare", "📥 Handling device result, resultCode: " + resultCode);

      if (resultCode == android.app.Activity.RESULT_OK && data != null) {
        String deviceType = call.getString("deviceType", "HEMOGLOBIN");
        JSObject result = parseDeviceResult(data, deviceType);

        Log.d("NovaIcare", "📥 Result received for " + deviceType + ": " + result.toString());

        // Notify device-specific listeners
        notifyDeviceListeners(deviceType, result);
        call.resolve(result);
      } else if (resultCode == android.app.Activity.RESULT_CANCELED) {
        Log.w("NovaIcare", "⚠️ Operation cancelled by user");

        JSObject cancelledResult = new JSObject();
        cancelledResult.put("success", false);
        cancelledResult.put("cancelled", true);
        cancelledResult.put("error", "Operation cancelled by user");
        cancelledResult.put("resultCode", resultCode);
        cancelledResult.put("timestamp", System.currentTimeMillis());

        call.resolve(cancelledResult); // Use resolve instead of reject for user cancellation
      } else {
        Log.w("NovaIcare", "❌ Operation failed with result code: " + resultCode);

        JSObject errorResult = new JSObject();
        errorResult.put("success", false);
        errorResult.put("error", "Operation failed");
        errorResult.put("resultCode", resultCode);
        errorResult.put("timestamp", System.currentTimeMillis());

        call.resolve(errorResult); // Use resolve to provide structured error info
      }
    } catch (Exception e) {
      Log.e("NovaIcare", "❌ Critical error handling stethoscope result", e);

      JSObject errorResult = new JSObject();
      errorResult.put("success", false);
      errorResult.put("error", "Critical error: " + e.getMessage());
      errorResult.put("errorType", e.getClass().getSimpleName());
      errorResult.put("resultCode", resultCode);
      errorResult.put("timestamp", System.currentTimeMillis());

      call.resolve(errorResult); // Use resolve to ensure client gets error info
    }
  }

  private JSObject parseDeviceResult(Intent data, String deviceType) {
    JSObject result = new JSObject();
    result.put("success", true);
    result.put("deviceType", deviceType);
    result.put("timestamp", System.currentTimeMillis());

    // Extract device-specific data fields based on reference implementation
    switch (deviceType) {
      case "PULSE_OXIMETER":
        // Extract SPO2 and pulse rate values
        String spo2Value = data.getStringExtra("spo2");
        String pulseRateValue = data.getStringExtra("pulse_rate");

        Log.d("NovaIcare", "📊 PULSE_OXIMETER data - spo2: " + spo2Value + ", pulse_rate: " + pulseRateValue);
        Log.d("NovaIcare", "Intent extras: " + data.getExtras());

        result.put("spo2", spo2Value);
        result.put("pulse_rate", pulseRateValue);

        // Also extract additional SPO2 fields if available
        if (data.getStringExtra("file_name") != null) {
          result.put("file_name", data.getStringExtra("file_name"));
        }
        break;

      case "THERMOMETER":
        // Extract temperature values
        String celcius = data.getStringExtra("celcius");
        String fahrenheit = data.getStringExtra("fahrenheit");
         String sensorType = data.getStringExtra("sensor_type"); // ✅ add this
        Log.d("NovaIcare", "🌡️ THERMOMETER data - celcius: " + celcius + ", fahrenheit: " + fahrenheit);

        result.put("celcius", celcius);
        result.put("fahrenheit", fahrenheit);
            result.put("sensor_type", sensorType); // ✅ add this
        break;

      case "BLOOD_PRESSURE":
        // Extract blood pressure values
        String systolic = data.getStringExtra("systolic");
        String diastolic = data.getStringExtra("diastolic");
        String bpPulseRate = data.getStringExtra("pulse_rate");

        Log.d("NovaIcare", "🩺 BLOOD_PRESSURE data - systolic: " + systolic + ", diastolic: " + diastolic + ", pulse_rate: " + bpPulseRate);

        result.put("systolic", systolic);
        result.put("diastolic", diastolic);
        result.put("pulse_rate", bpPulseRate);
        break;

      case "STETHOSCOPE":
        // Extract stethoscope values
        String stethoReading = data.getStringExtra("Stetho_Reading");
        String stethoValue = data.getStringExtra("stetho_value");

        Log.d("NovaIcare", "🔊 STETHOSCOPE data - Stetho_Reading: " + stethoReading + ", stetho_value: " + stethoValue);

        result.put("Stetho_Reading", stethoReading);
        result.put("stetho_value", stethoValue);
        break;

      case "ECG":
        // Extract ECG values
        String ecgPulseRate = data.getStringExtra("pulse_rate");
        String ecgLead1 = data.getStringExtra("ecg_lead1");
        String ecgLead2 = data.getStringExtra("ecg_lead2");
        String ecgLead3 = data.getStringExtra("ecg_lead3");
        String ecgFileName = data.getStringExtra("file_name");

        Log.d("NovaIcare", "📈 ECG data - pulse_rate: " + ecgPulseRate + ", leads: " + ecgLead1 + "," + ecgLead2 + "," + ecgLead3 + ", file: " + ecgFileName);

        result.put("pulse_rate", ecgPulseRate);
        result.put("ecg_lead1", ecgLead1);
        result.put("ecg_lead2", ecgLead2);
        result.put("ecg_lead3", ecgLead3);
        result.put("file_name", ecgFileName);

        // Extract additional ECG fields if available (from reference implementation)
        if (data.getStringExtra("ecg_avr") != null) {
          result.put("ecg_avr", data.getStringExtra("ecg_avr"));
        }
        if (data.getStringExtra("ecg_avl") != null) {
          result.put("ecg_avl", data.getStringExtra("ecg_avl"));
        }
        break;
        case "HEMOGLOBIN":

        String hemoglobinValue = data.getStringExtra("hemoglobin_value");
        String hemoglobinFileName = data.getStringExtra("file_name");
        Log.d("NovaIcare", "🩸 HEMOGLOBIN data - hemoglobin_value: " + hemoglobinValue + ", file_name: " + hemoglobinFileName);
        result.put("hemoglobin_value", hemoglobinValue);
        result.put("file_name", hemoglobinFileName);
        break;


        case "optical_reader":

        String opticalImagePath = data.getStringExtra("opticalImagePath");
        String testName = data.getStringExtra("testName");
        String testResult = data.getStringExtra("testResult");
        String imageName = data.getStringExtra("imageName");
//        String sensorType = data.getStringExtra("sensor_type");
        String imageFileUri = data.getStringExtra("imageFileUri");

        result.put("opticalImagePath", opticalImagePath);
        result.put("testName", testName);
        result.put("testResult", testResult);
        result.put("imageName", imageName);
//        result.put("sensor_type", sensorType);
        result.put("imageFileUri", imageFileUri);
        result.put("source", "nova_icare_mainactivity");
        break;

      default:
        Log.w("NovaIcare", "⚠️ Unknown device type: " + deviceType + " - extracting generic data");
        // For unknown device types, try to extract common fields
        if (data.getStringExtra("spo2") != null) {
          result.put("spo2", data.getStringExtra("spo2"));
        }
        if (data.getStringExtra("pulse_rate") != null) {
          result.put("pulse_rate", data.getStringExtra("pulse_rate"));
        }
        break;
    }

    // Add common fields that might be present for any device
    if (data.getStringExtra("error") != null) {
      result.put("error", data.getStringExtra("error"));
    }
    if (data.getStringExtra("sensor_type") != null) {
      result.put("sensor_type", data.getStringExtra("sensor_type"));
    }

    // Add any battery level information if available
    if (data.getStringExtra("battery_level") != null) {
      result.put("battery_level", data.getStringExtra("battery_level"));
    }

    // Add signal quality information if available
    if (data.getStringExtra("signal_quality") != null) {
      result.put("signal_quality", data.getStringExtra("signal_quality"));
    }

    Log.d("NovaIcare", "📦 Final parsed result for " + deviceType + ": " + result.toString());

    return result;
  }

  private void notifyDeviceListeners(String deviceType, JSObject result) {
    switch (deviceType) {
      case "PULSE_OXIMETER":
        notifyListeners("PulseOximeterResult", result);
        break;
      case "THERMOMETER":
        notifyListeners("ThermometerResult", result);
        break;
      case "BLOOD_PRESSURE":
        notifyListeners("BloodPressureResult", result);
        break;
      case "STETHOSCOPE":
        notifyListeners("StethoscopeResult", result);
        break;
      case "ECG":
        notifyListeners("ECGResult", result);
        break;
      case "optical_reader":
         notifyListeners("opticalreaderlis", result);
         break;
      case "HEMOGLOBIN":
      notifyListeners("HemoglobinmeterResult", result);
      break;
    }
    // Always notify generic listener
    notifyListeners("DeviceResult", result);
  }

  public void sendSpo2Result(String spo2, String pulseRate) {
    JSObject result = new JSObject();
    result.put("spo2", spo2);
    result.put("pulse_rate", pulseRate);

    notifyListeners("PulseOximeterResult", result);

    if (savedCall != null) {
      savedCall.resolve(result);
      savedCall = null;
    }
  }

// public void sendThermometerResult(String fahrenheit, String celcius) {
//     JSObject result = new JSObject();
//     result.put("fahrenheit", fahrenheit);
//     result.put("celcius", celcius);
//     result.put("sensor_type", "Thermometer"); // Optional, based on your logs

//     // ✅ Add log before notifying JS
//     Log.d("NovaIcare", "✅ Sent temperature data to JS layer: " + result.toString());

//     notifyListeners("ThermometerResult", result);

//     if (savedCall != null) {
//         savedCall.resolve(result);
//         savedCall = null;
//     }
// }
private JSObject parseDeviceResultnew(Intent data, String deviceType) {
    JSObject result = new JSObject();
    result.put("success", true);
    result.put("deviceType", deviceType);
    result.put("timestamp", System.currentTimeMillis());

    switch (deviceType) {
        case "PULSE_OXIMETER":
            String spo2Value = data.getStringExtra("spo2");
            String pulseRateValue = data.getStringExtra("pulse_rate");

            Log.d("NovaIcare", "📊 PULSE_OXIMETER data - spo2: " + spo2Value + ", pulse_rate: " + pulseRateValue);
            Log.d("NovaIcare", "Intent extras: " + data.getExtras());

            result.put("spo2", spo2Value);
            result.put("pulse_rate", pulseRateValue);
            if (data.getStringExtra("file_name") != null) {
                result.put("file_name", data.getStringExtra("file_name"));
            }
            break;

        case "THERMOMETER":
            String celcius = data.getStringExtra("celcius");
            String fahrenheit = data.getStringExtra("fahrenheit");

            Log.d("NovaIcare", "🌡️ THERMOMETER data - celcius: " + celcius + ", fahrenheit: " + fahrenheit);

            result.put("celcius", celcius);
            result.put("fahrenheit", fahrenheit);
            break;

        case "BLOOD_PRESSURE":
            String systolic = data.getStringExtra("systolic");
            String diastolic = data.getStringExtra("diastolic");
            String bpPulseRate = data.getStringExtra("pulse_rate");

            Log.d("NovaIcare", "🩺 BLOOD_PRESSURE data - systolic: " + systolic + ", diastolic: " + diastolic + ", pulse_rate: " + bpPulseRate);

            result.put("systolic", systolic);
            result.put("diastolic", diastolic);
            result.put("pulse_rate", bpPulseRate);
            break;

        case "STETHOSCOPE":
            String stethoReading = data.getStringExtra("Stetho_Reading");
            String stethoValue = data.getStringExtra("stetho_value");

            Log.d("NovaIcare", "🔊 STETHOSCOPE data - Stetho_Reading: " + stethoReading + ", stetho_value: " + stethoValue);

            result.put("Stetho_Reading", stethoReading);
            result.put("stetho_value", stethoValue);
            break;

        case "ECG":
            String ecgPulseRate = data.getStringExtra("pulse_rate");
            String ecgLead1 = data.getStringExtra("ecg_lead1");
            String ecgLead2 = data.getStringExtra("ecg_lead2");
            String ecgLead3 = data.getStringExtra("ecg_lead3");
            String ecgFileName = data.getStringExtra("file_name");

            Log.d("NovaIcare", "📈 ECG data - pulse_rate: " + ecgPulseRate + ", leads: " + ecgLead1 + "," + ecgLead2 + "," + ecgLead3 + ", file: " + ecgFileName);

            result.put("pulse_rate", ecgPulseRate);
            result.put("ecg_lead1", ecgLead1);
            result.put("ecg_lead2", ecgLead2);
            result.put("ecg_lead3", ecgLead3);
            result.put("file_name", ecgFileName);

            if (data.getStringExtra("ecg_avr") != null) {
                result.put("ecg_avr", data.getStringExtra("ecg_avr"));
            }
            if (data.getStringExtra("ecg_avl") != null) {
                result.put("ecg_avl", data.getStringExtra("ecg_avl"));
            }
            break;

        case "HEMOGLOBIN":
            String hemoglobinValue = data.getStringExtra("hemoglobin_value");
            String hemoglobinFileName = data.getStringExtra("file_name");

            Log.d("NovaIcare", "🩸 HEMOGLOBIN data - hemoglobin_value: " + hemoglobinValue + ", file_name: " + hemoglobinFileName);

            result.put("hemoglobin_value", hemoglobinValue);
            result.put("file_name", hemoglobinFileName);
            break;

        case "optical_reader":
            String opticalImagePath = data.getStringExtra("opticalImagePath");
            String testName = data.getStringExtra("testName");
            String testResult = data.getStringExtra("testResult");
            String imageName = data.getStringExtra("imageName");
            String imageFileUri = data.getStringExtra("imageFileUri");

            result.put("opticalImagePath", opticalImagePath); // ✅ fixed typo
            result.put("testName", testName);
            result.put("testResult", testResult);
            result.put("imageName", imageName);
            result.put("imageFileUri", imageFileUri);
            result.put("source", "nova_icare_mainactivity");
            break;

        default:
            Log.w("NovaIcare", "⚠️ Unknown device type: " + deviceType + " - extracting generic data");
            if (data.getStringExtra("spo2") != null) {
                result.put("spo2", data.getStringExtra("spo2"));
            }
            if (data.getStringExtra("pulse_rate") != null) {
                result.put("pulse_rate", data.getStringExtra("pulse_rate"));
            }
            break;
    }

    // ✅ Common fields (add only once)
    if (data.getStringExtra("sensor_type") != null) {
        result.put("sensor_type", data.getStringExtra("sensor_type"));
    }
    if (data.getStringExtra("error") != null) {
        result.put("error", data.getStringExtra("error"));
    }
    if (data.getStringExtra("battery_level") != null) {
        result.put("battery_level", data.getStringExtra("battery_level"));
    }
    if (data.getStringExtra("signal_quality") != null) {
        result.put("signal_quality", data.getStringExtra("signal_quality"));
    }

    Log.d("NovaIcare", "📦 Final parsed result for " + deviceType + ": " + result.toString());

    return result;
}



public void sendThermometerResult(String fahrenheit, String celcius) {
    JSObject result = new JSObject();
    result.put("fahrenheit", fahrenheit);
    result.put("celcius", celcius);
    result.put("sensor_type", "THERMOMETER"); // ✅ Correct lowercase to match Angular enum

    // ✅ Add log before notifying JS
    Log.d("NovaIcare", "✅ Sent temperature data to JS layer: " + result.toString());

    notifyListeners("ThermometerResult", result);

    if (savedCall != null) {
        savedCall.resolve(result);
        savedCall = null;
    }
}


public void sendHemoResult(String hemodata) {
  JSObject result = new JSObject();
  result.put("hemoglobin_value",hemodata);
  Log.d("NovaIcare", "🩸 HEMOGLOBIN data - hemoglobin_value: " + hemodata);
  notifyListeners("HemoglobinmeterResult", result);

  if (savedCall != null) {
    savedCall.resolve(result);
    savedCall = null;
  }
}

public void sendECGResult(JSONObject result) {
  JSObject result1 = new JSObject();

  try {
    Iterator<String> keys = result.keys();
    while (keys.hasNext()) {
      String key = keys.next();
      Object value = result.get(key);

      // You may need to handle nested JSONObjects or arrays carefully
      if (value instanceof JSONObject) {
        result1.put(key, new JSObject(value.toString()));
      } else if (value instanceof JSONArray) {
        result1.put(key, new JSONArray(value.toString()));
      } else {
        result1.put(key, value);
      }
    }

    notifyListeners("ECGResult", result1);

    if (savedCall != null) {
      savedCall.resolve(result1);
      savedCall = null;
    }
  } catch (JSONException e) {
    Log.e("NovaIcare", "❌ Failed to convert JSONObject to JSObject", e);
    if (savedCall != null) {
      savedCall.reject("Failed to parse ECG result: " + e.getMessage());
      savedCall = null;
    }
  }
  
   }

}
