import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  On<PERSON>estroy,
  AfterViewInit,
  Inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import * as Highcharts from 'highcharts/highstock';
// import * as Highcharts from 'highcharts';
import { WebsocketsService } from 'src/app/core/services/websocket.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';

// import Scrollbar from 'highcharts/modules/scrollbar';

// Scrollbar(Highcharts); // <--


@Component({
  selector: 'app-spo2-device',
  templateUrl: './spo2-device.page.html',
  styleUrls: ['./spo2-device.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule,MatTooltipModule],
})
export class Spo2DevicePage implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('chartContainer') chartContainer!: ElementRef;

  battery = 0;
  spo2_perc = 0;
  spo2TowPercentage = 0;
  spo2SetScrollFlag = 0;
  spo2Data: any = [];
  spo2Chart: any;
  spo2Series: any;
  spo2highchart: any = Highcharts;
  spo2highchartNew: any = Highcharts;
  spo2Interval: any;
  isBLE: string = 'true';
  spoTowPercentage: number = 0;


  valSubscription: Subscription | null = null;
  spo2DeviceVal: any = [];
  constructor(
    private modalCtrl: ModalController,
    private websocketsService: WebsocketsService,
      @Inject(MAT_DIALOG_DATA) public data: any,
  private dialogRef: MatDialogRef<any>
  ) {}

  ngOnInit() {
    // Simulate WebSocket message subscription
    this.valSubscription = new Subscription(); // Replace with real WebSocket

    
    this.valSubscription = this.websocketsService.bleValueObs$.subscribe(
      (data) => {
        console.log(data, 'data');

        if (data.match(/plotSpo2Graph/g)) {
          this.spo2DeviceVal = data.split('~')[1];
          this.plotSpo2Graph(this.spo2DeviceVal);
        } else if (data.match(/completed/g)) {
          // this.helperSvc.setTickerSubscriber(true);
          this.spo2Data = [];
          this.spo2highchart = Highcharts;
          this.spo2highchartNew = Highcharts;
          this.spo2SetScrollFlag = 0;
        }
        if (data.match(/setCurrentSpo2/g)) {
          this.spoTowPercentage = Number((data.split("~")[1]));
        }
        if (data.match(/changeBatteryStatus/g)) {
          var batteryBit = Number(data.split("~")[1]);
          if (batteryBit < 4) {
            this.battery = 10;
          } else if (batteryBit == 4) {
            this.battery = 33;
          }
          else if (batteryBit == 5) {
            this.battery = 66;
          }
          else if (batteryBit == 6) {
            this.battery = 100;
          }
        }
      }
    );
  }

  ngAfterViewInit(): void {
    this.initializeChart(this.isBLE);
  }

  ngOnDestroy(): void {
    clearInterval(this.spo2Interval);
    if (this.valSubscription) this.valSubscription.unsubscribe();
  }

  initializeChart(isBle: string) {
    const self = this;

    this.spo2highchartNew = Highcharts.chart(
      this.chartContainer.nativeElement,
      {
        chart: {
          type: 'spline',
          scrollablePlotArea: {
          minWidth: 100, // 👈 allows scrolling
          scrollPositionX: 1,
        },
          events: {
            load: function () {
              self.spo2Chart = this;
              self.spo2Series = this.series[0];
            },
          },
        },
        tooltip: { enabled: false },
        title: { text: '' },
        xAxis: {
          title: { text: 'Time->' },
          type: 'datetime',
          dateTimeLabelFormats: { second: '%H:%M:%S' },
          labels: { enabled: false },
        },
        yAxis: {
          tickInterval: 50,
          title: { text: '' },
          visible: false,
        },
        legend: { enabled: false },
        exporting: { enabled: true },
        series: [
          {
            type: 'spline', // Add this line to resolve overload issue
            name: 'spo2 data',
            color: 'green',
            animation: { duration: 500 },
            data: [],
          },
        ],
        scrollbar: {
          enabled: true,
          barBackgroundColor: 'gray',
          barBorderRadius: 7,
          buttonArrowColor: 'yellow',
          trackBorderColor: 'silver',
        },
      }
    );
  }

  plotSpo2Graph(data: any) {
    let arr = JSON.parse(data);
    if (this.isBLE === 'false') arr.splice(0, 1);
    this.spo2Data.push(...arr);
    if (this.spo2Series) this.spo2Series.setData(this.spo2Data);

    if (this.isBLE === 'true') {
      if (this.spo2SetScrollFlag > 11) {
        const x_max = this.spo2Chart.xAxis[0].max;
        this.spo2Chart.xAxis[0].setExtremes(x_max - 550, x_max + 50);
      }
    } else {
      if (this.spo2SetScrollFlag > 5) {
        const x_max = this.spo2Chart.xAxis[0].max;
        this.spo2Chart.xAxis[0].setExtremes(x_max - 3750, x_max + 750);
      }
    }
    this.spo2SetScrollFlag++;
  }
onDialogClose() {
  this.dialogRef.close();
}

}
