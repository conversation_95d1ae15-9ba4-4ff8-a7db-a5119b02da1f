// File: electron/main.js

const { app, BrowserWindow, globalShortcut, session } = require('electron');
const path = require('path');

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      contextIsolation: true,
      nodeIntegration: false,
      webSecurity: false,
      allowRunningInsecureContent: true,
      enableRemoteModule: false,
      experimentalFeatures: true,
      allowEval: true,
      additionalArguments: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    }
  });

  // Modify request headers
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    details.requestHeaders['User-Agent'] = 'RemediElectronApp';
    callback({ requestHeaders: details.requestHeaders });
  });

  // Set Content Security Policy
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https: http: ws: wss:; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
          "style-src 'self' 'unsafe-inline'; " +
          "img-src 'self' data: https: http:; " +
          "connect-src 'self' https: http: ws: wss:; " +
          "font-src 'self' data:;"
        ]
      }
    });
  });

  // Load Angular index.html (ensure it's built to dist/remedi-app/)
  // const indexPath = path.join(__dirname, '../dist/remedi-app/index.html');
  const indexPath = path.join(__dirname, '../dist/remedi-angular-native/index.html');
  // win.loadFile(path.join(__dirname, '../www/index.html'));
  console.log('Trying to load:', indexPath);
  win.loadFile(indexPath).catch(err => console.error('Load error:', err));

  // Only open DevTools in development mode
  if (process.env.NODE_ENV === 'development') {
    win.webContents.openDevTools();
  }

  // Navigation shortcut and electronAPI injection
  win.webContents.on('did-finish-load', () => {
    globalShortcut.register('Alt+Left', () => {
      if (win.webContents.canGoBack()) win.webContents.goBack();
    });

    win.webContents.executeJavaScript(`
      window.electronAPI = {
        isElectron: true,
        platform: '${process.platform}',
        version: '${process.versions.electron}'
      };
    `);
  });
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});
