<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Package visibility queries for Android 11+ (API 30+) -->
    <queries>
        <!-- NovaICare app package -->
        <package android:name="com.neurosynaptic.nova_icare" />
        
        <!-- Intent queries for NovaICare activities -->
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.DEFAULT" />
        </intent>
        
        <!-- Query for specific NovaICare device activities -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
        </intent>
    </queries>

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- Additional permissions for medical device communication -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" 
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true">

        <activity
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
            android:name=".MainActivity"
            android:label="@string/title_activity_main"
            android:theme="@style/AppTheme.NoActionBarLaunch"
            android:launchMode="singleTask"
            android:exported="true"
            android:taskAffinity=""
            android:excludeFromRecents="false">

            <!-- Main launcher intent filter -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Intent filter for receiving medical device data -->
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="application/medical-device-data" />
            </intent-filter>

            <!-- Intent filter for NovaICare result data -->
            <intent-filter>
                <action android:name="com.neurosynaptic.nova_icare.RESULT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- Intent filter for generic medical device results -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="remedi" android:host="medical-device" />
            </intent-filter>

            <!-- Intent filter for NovaICare callback (enterprise pattern) -->
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>

            <!-- Intent filter for direct NovaICare communication -->
            <intent-filter>
                <action android:name="com.neurosynaptic.remedi_nova.DEVICE_RESULT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"></meta-data>
        </provider>

    </application>

</manifest>
