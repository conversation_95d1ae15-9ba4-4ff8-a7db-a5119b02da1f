{"name": "remedi-app", "version": "0.0.1", "author": "Ionic Framework", "main": "electron/main.js", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --base-href ./", "build:electron": "npm run build && electron .", "start:electron": "concurrently \"npm run build -- --watch\" \"wait-on dist/remedi-app/index.html && electron .\"", "electron": "electron .", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "android:build": "npm run build && npx cap sync android", "android:run": "npm run android:build && npx cap run android", "android:open": "npx cap open android", "android:sync": "npx cap sync android", "android:dev": "npm run build && npx cap sync android && npx cap run android --live-reload", "android:release": "npm run build && npx cap sync android && cd android && ./gradlew assembleRelease", "android:debug": "npm run build && npx cap sync android && cd android && ./gradlew assembleDebug", "android:install": "npm run android:debug && cd android && ./gradlew installDebug", "android:logs": "npx cap run android --target --livereload --external && adb logcat", "cap:sync": "npx cap sync", "cap:add:android": "npx cap add android", "cap:update": "npx cap update"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/cdk": "^20.1.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.1.3", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "@capacitor/android": "^7.4.1", "@capacitor/app": "7.0.1", "@capacitor/core": "^7.4.1", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@fortawesome/fontawesome-free": "^6.7.2", "@ionic/angular": "^8.0.0", "@ionic/core": "^6.4.2", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@stencil/core": "^2.22.2", "crypto-js": "^4.2.0", "d3": "^7.9.0", "highcharts": "^12.3.0", "highcharts-angular": "^5.1.0", "ionicons": "^6.0.4", "jquery": "^3.7.1", "js-sha512": "^0.9.0", "rxjs": "~7.8.0", "sweetalert2": "^11.22.2", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@capacitor/cli": "^7.4.1", "@ionic/angular-toolkit": "^12.0.0", "@types/crypto-js": "^4.2.2", "@types/d3": "^7.4.3", "@types/jasmine": "~5.1.0", "@types/jquery": "^3.5.32", "@types/node": "^24.1.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "electron": "^37.2.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.0"}, "description": "An Ionic project"}