<div style="border: 1px solid #007bff; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
  <!-- Header row: Title on left, Close button on right -->
  <div style="display: flex; justify-content: space-between; align-items: center;">
    <h3 style="color: #007bff; margin: 0;">🫀 Pulse Oximeter</h3>
    <button (click)="onDialogClose()" style="background: transparent; border: none; color: #dc3545; font-size: 20px; cursor: pointer;">
      ✕
    </button>
  </div>

  <!-- Action button -->
  <button *ngIf="isReading" (click)="launchPulseOximeter()" style="margin-top: 10px; background-color: #007bff; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
    SPO2
  </button>

  <!-- Display Results -->
  <ng-container *ngIf="resultdata; else noData">
    <p><strong>SpO₂:</strong> {{ resultdata.spo2 }}%</p>
    <p><strong>Pulse Rate:</strong> {{ resultdata.pulse_rate }} bpm</p>

    <p *ngIf="resultdata.batteryLevel != null">
      <strong>Battery:</strong> {{ resultdata.batteryLevel }}%
    </p>

    <p *ngIf="resultdata.signalQuality">
      <strong>Signal Quality:</strong> {{ resultdata.signalQuality }}
    </p>

    <p><strong>Timestamp:</strong> {{ resultdata.timestamp | date: 'medium' }}</p>

    <p *ngIf="resultdata.error" style="color: red;">
      <strong>Error:</strong> {{ resultdata.error }}
    </p>
  </ng-container>

  <ng-template #noData>
    <p style="color: #6c757d; font-style: italic;">No data available</p>
  </ng-template>
</div>
