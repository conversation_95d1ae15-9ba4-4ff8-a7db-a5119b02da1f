<!-- Wrapper -->
<div style="display: flex; flex-direction: column; height: 100vh; font-family: 'Segoe UI', sans-serif;">

    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; background-color: #007bff; color: white; padding: 15px 25px;">
        <div style="font-size: 18px; font-weight: bold;">ReMeDi</div>
        <button (click)="logout()" style="background-color: white; color: #E91E63; border: none; padding: 6px 16px; border-radius: 6px; font-weight: 600; cursor: pointer; transition: 0.3s;">
      Logout
    </button>
    </div>

    <!-- Content -->
    <div style="flex: 1; overflow-y: auto; padding: 30px 20px; display: flex; flex-direction: column; gap: 50px;">

        <!-- Jetty Devices -->
        <div style="text-align: center;">
            <h3 style="margin-bottom: 25px; font-size: 20px; font-weight: 600;">Jetty Devices</h3>
            <div style="display: flex; gap: 40px; flex-wrap: wrap; justify-content: center;">
                <div class="device-card" (click)="handleJettyClick('SPO2', 'openSpo2ComponentDevice')">
                    <img src="assets/images/heart-attack.png" alt="SPO2" />
                    <div>SPO2</div>
                </div>

                <div class="device-card" (click)="handleJettyClick('Hemoglobin', 'openSpo2ManualEntryPage')">
                    <img src="assets/images/hemoglobin.png" alt="HB" />
                    <div>Hemoglobin</div>
                </div>

                <!-- <div class="device-card" (click)="handleJettyClick('Temperature', 'openThermometerPage')">
                    <img src="assets/images/thermometer.png" alt="Temp" />
                    <div>Temperature</div>
                </div> -->

                <div class="device-card" (click)="handleJettyClick('ECG', 'openEcgComponentDevice')">
                    <img src="assets/images/heart-rate.png" alt="ECG" />
                    <div>Electro Cardio Gram</div>
                </div>
                  <!-- <div class="device-card" (click)="openStethoscopeDevicePage()">
                    <img src="assets/images/sthethoscope.png" alt="ECG" />
                    <div>Stethoscope</div>
                </div> -->
                <div class="device-card" (click)="handleJettyClick('Optical Reader', 'openRapidTestDevice')">
                    <img src="assets/images/OpticalReade_Web.png" alt="OR" />
                    <div>Optical Reader</div>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <div *ngIf="jettyMessage" style="text-align: center; font-weight: 600; color: #ff5722; font-size: 16px;">
            {{ jettyMessage }}
        </div>

        <!-- iCare Devices -->
        <div style="text-align: center;">
            <h3 style="margin-bottom: 25px; font-size: 20px; font-weight: 600;">iCare Devices</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap; justify-content: center;">
                <button class="device-button" (click)="openIcareSpo2Page()">SPO2</button>
                <button class="device-button" (click)="openHemoglobinPage()">Hemoglobin</button>
                <!-- <button class="device-button" (click)="openIcareThermometerPage()">Temperature</button> -->
                <button class="device-button" (click)="openECGICarePage()">Electro Cardio Gram</button>
                <button class="device-button" (click)="openicarerapidtest()">Optical Reader</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div style="background-color: #f0f0f0; padding: 12px; text-align: center; font-size: 14px; color: #333;">
        <!-- Optional footer content -->
    </div>
</div>