<div class="ecg-container">

  <!-- Header with Title and Close Button -->
  <div class="ecg-header">
    <h2>Electrocardiogram (ECG)</h2>
    <button class="close-btn" (click)="closeEcgSection()">✖</button>
  </div>

  <!-- Launch ECG Button -->
  <button class="launch-btn" (click)="launchIcarePluginDevice()">
    ▶ Start Electrocardiogram
  </button>

  <!-- ECG Result Section -->
  <div class="ecg-result" *ngIf="resultdata">
    <h4>ECG Result</h4>
    <p><strong>Pulse Rate:</strong> {{ resultdata?.pulse_rate || 'N/A' }}</p>
    <p><strong>ECG File:</strong> {{ resultdata?.ecg_file_name || 'Not available' }}</p>
  </div>

</div>
