<!-- Hemoglobin Results -->
<div style="border: 1px solid #28a745; padding: 16px; border-radius: 8px; background-color: #f8f9fa; margin-top: 20px; position: relative;">
  <!-- Close button -->
  <button 
  (click)="closeDialog()"
  aria-label="Close"
  style="position: absolute; top: 8px; right: 8px; background: transparent; border: none; font-size: 20px; font-weight: bold; cursor: pointer; color: #6c757d;">
  ×
</button>

  <h3 style="color: #28a745; margin-top: 0;">🩸 Hemoglobin</h3>
  
  <button 
    (click)="launchIassist()" 
    style="background-color: #28a745; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
    Measure Hemoglobin
  </button>
  
  <div style="margin-top: 16px;">
    <!-- Show hemoglobin value if available -->
    <div *ngIf="resultdata?.hemoglobin_value">
      <p style="margin: 8px 0; color: #28a745; font-weight: bold;">
        <strong>Hemoglobin:</strong> {{ resultdata.hemoglobin_value }} g/dL
      </p>
      <p style="font-size: 12px; color: #666; margin: 4px 0;">
        Source: {{ resultdata.source }}
      </p>
    </div>
    
    <!-- Show loading state -->
    <div *ngIf="isReading && !resultdata?.hemoglobin_value" style="color: #007bff; font-style: italic;">
      Reading hemoglobin data...
    </div>
    
    <!-- Show error if any -->
    <div *ngIf="resultdata?.error" style="color: #dc3545;">
      <p style="margin: 8px 0;">
        <strong>Error:</strong> {{ resultdata.error }}
      </p>
    </div>
    
    <!-- Show no data message -->
    <div *ngIf="!resultdata && !isReading">
      <p style="color: #6c757d; font-style: italic; margin: 8px 0;">
        No Hemoglobin data available.
      </p>
    </div>
  </div>
</div>
